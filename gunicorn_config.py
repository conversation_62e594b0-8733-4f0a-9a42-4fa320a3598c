#!/usr/bin/env python3
"""
Gunicorn configuration for multi-user CDN video player
Optimized for low CPU and RAM usage with high concurrency
"""

import multiprocessing
import os

# Server socket
bind = "0.0.0.0:5000"
backlog = 2048

# Worker processes (auto-detect CPU cores, cross-platform)
workers = min(4, max(1, multiprocessing.cpu_count()))  # At least 1, max 4 workers
worker_class = "eventlet"  # Async worker for better concurrency (Python 3.13 compatible)
worker_connections = 1000
max_requests = 1000  # Restart workers after 1000 requests to prevent memory leaks
max_requests_jitter = 50
preload_app = True  # Share memory between workers

# Timeouts
timeout = 30
keepalive = 2
graceful_timeout = 30

# Memory optimization
worker_tmp_dir = "/dev/shm"  # Use RAM for temporary files
tmp_upload_dir = "/tmp"

# Logging
accesslog = "-"  # Log to stdout
errorlog = "-"   # Log to stderr
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# Process naming
proc_name = "cdn_video_player"

# Security
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190

# Performance tuning
sendfile = True
tcp_nodelay = True
reuse_port = True

def when_ready(server):
    """Called when the server is ready to serve requests"""
    print("🚀 Multi-user CDN Video Player ready!")
    print(f"👥 Workers: {workers}")
    print(f"🔗 Listening on: {bind}")

def worker_int(worker):
    """Called when a worker receives the INT or QUIT signal"""
    print(f"🔄 Worker {worker.pid} interrupted")

def on_exit(server):
    """Called when the server is shutting down"""
    print("🛑 CDN Video Player shutting down")
