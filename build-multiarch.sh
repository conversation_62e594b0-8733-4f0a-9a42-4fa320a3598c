#!/bin/bash
# Multi-architecture Docker build script
# Supports AMD64, ARM64, and ARM/v7 platforms

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="cdn-video-player"
REGISTRY="your-registry"  # Change this to your Docker registry
VERSION=${1:-latest}

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_buildx() {
    log_info "Checking Docker Buildx support..."
    
    if ! docker buildx version &> /dev/null; then
        log_error "Docker Buildx is not available"
        log_info "Please install Docker Desktop or enable Buildx"
        exit 1
    fi
    
    # Create builder if it doesn't exist
    if ! docker buildx ls | grep -q "multiarch"; then
        log_info "Creating multi-architecture builder..."
        docker buildx create --name multiarch --use
        docker buildx inspect --bootstrap
    else
        docker buildx use multiarch
    fi
    
    log_success "Docker Buildx ready"
}

build_local() {
    log_info "Building for local platform..."
    docker build -t $IMAGE_NAME:$VERSION .
    log_success "Local build completed"
}

build_multiarch() {
    log_info "Building for multiple architectures..."
    
    # Platforms to build for
    PLATFORMS="linux/amd64,linux/arm64,linux/arm/v7"
    
    log_info "Building for platforms: $PLATFORMS"
    
    docker buildx build \
        --platform $PLATFORMS \
        --tag $IMAGE_NAME:$VERSION \
        --tag $IMAGE_NAME:latest \
        --push \
        .
    
    log_success "Multi-architecture build completed"
}

build_and_push() {
    log_info "Building and pushing to registry..."
    
    if [ "$REGISTRY" = "your-registry" ]; then
        log_warning "Please set REGISTRY variable in script"
        log_info "Building locally only..."
        build_local
        return
    fi
    
    PLATFORMS="linux/amd64,linux/arm64,linux/arm/v7"
    
    docker buildx build \
        --platform $PLATFORMS \
        --tag $REGISTRY/$IMAGE_NAME:$VERSION \
        --tag $REGISTRY/$IMAGE_NAME:latest \
        --push \
        .
    
    log_success "Build and push completed"
}

test_image() {
    log_info "Testing built image..."
    
    # Test local image
    docker run --rm -d --name test-cdn -p 5001:5000 $IMAGE_NAME:$VERSION
    
    # Wait for startup
    sleep 10
    
    # Test health endpoint
    if curl -f http://localhost:5001/api/stats &> /dev/null; then
        log_success "Image test passed"
    else
        log_error "Image test failed"
        docker logs test-cdn
    fi
    
    # Cleanup
    docker stop test-cdn
}

show_usage() {
    echo "Usage: $0 [VERSION] [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  local     - Build for local platform only"
    echo "  multiarch - Build for multiple architectures"
    echo "  push      - Build and push to registry"
    echo "  test      - Test the built image"
    echo ""
    echo "Examples:"
    echo "  $0 v1.0.0 local"
    echo "  $0 latest multiarch"
    echo "  $0 v1.0.0 push"
}

# Main script
case "${2:-local}" in
    "local")
        build_local
        ;;
    "multiarch")
        check_buildx
        build_multiarch
        ;;
    "push")
        check_buildx
        build_and_push
        ;;
    "test")
        test_image
        ;;
    *)
        show_usage
        exit 1
        ;;
esac

log_success "Build process completed!"
echo ""
log_info "Image: $IMAGE_NAME:$VERSION"
log_info "Platforms: AMD64, ARM64, ARM/v7 compatible"
log_info "Python: 3.8+ compatible"
