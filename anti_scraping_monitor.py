#!/usr/bin/env python3
"""
Anti-Scraping Monitoring System
Real-time monitoring and protection for the CDN player
"""

import json
import time
from datetime import datetime
from collections import defaultdict

class AntiScrapingMonitor:
    def __init__(self):
        self.suspicious_activities = []
        self.request_patterns = defaultdict(list)
        self.protection_stats = {
            'total_requests': 0,
            'suspicious_requests': 0,
            'devtools_detections': 0,
            'automation_detections': 0
        }
    
    def log_request(self, ip, user_agent, endpoint, suspicious=False):
        """Log incoming request"""
        self.protection_stats['total_requests'] += 1
        
        if suspicious:
            self.protection_stats['suspicious_requests'] += 1
            self.suspicious_activities.append({
                'timestamp': time.time(),
                'ip': ip,
                'user_agent': user_agent,
                'endpoint': endpoint,
                'type': 'suspicious_request'
            })
        
        # Track request patterns
        self.request_patterns[ip].append({
            'timestamp': time.time(),
            'endpoint': endpoint,
            'user_agent': user_agent
        })
    
    def log_devtools_detection(self, ip, video_id=None):
        """Log DevTools detection"""
        self.protection_stats['devtools_detections'] += 1
        self.suspicious_activities.append({
            'timestamp': time.time(),
            'ip': ip,
            'video_id': video_id,
            'type': 'devtools_detection'
        })
    
    def log_automation_detection(self, ip, user_agent):
        """Log automation tool detection"""
        self.protection_stats['automation_detections'] += 1

        self.suspicious_activities.append({
            'timestamp': time.time(),
            'ip': ip,
            'user_agent': user_agent,
            'type': 'automation_detection'
        })
    
    def analyze_patterns(self):
        """Analyze request patterns for suspicious behavior"""
        current_time = time.time()
        suspicious_ips = []
        
        for ip, requests in self.request_patterns.items():
            # Remove old requests (older than 1 hour)
            recent_requests = [r for r in requests if current_time - r['timestamp'] < 3600]
            self.request_patterns[ip] = recent_requests
            
            if len(recent_requests) > 100:  # Too many requests
                suspicious_ips.append(ip)
            
            # Check for rapid-fire requests
            if len(recent_requests) >= 10:
                time_diff = recent_requests[-1]['timestamp'] - recent_requests[-10]['timestamp']
                if time_diff < 10:  # 10 requests in 10 seconds
                    suspicious_ips.append(ip)
        
        return suspicious_ips
    
    def get_protection_report(self):
        """Generate protection report"""
        suspicious_ips = self.analyze_patterns()
        
        return {
            'stats': self.protection_stats,
            'suspicious_ips': suspicious_ips,
            'recent_activities': self.suspicious_activities[-20:],  # Last 20 activities
            'total_suspicious_activities': len(self.suspicious_activities),
            'protection_effectiveness': {
                'detection_rate': (self.protection_stats['suspicious_requests'] / max(self.protection_stats['total_requests'], 1)) * 100
            }
        }
    
    def save_report(self, filename='anti_scraping_report.json'):
        """Save protection report to file"""
        report = self.get_protection_report()
        report['generated_at'] = datetime.now().isoformat()
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        return report

# Global monitor instance
monitor = AntiScrapingMonitor()

def create_protection_dashboard():
    """Create HTML dashboard for monitoring"""
    dashboard_html = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anti-Scraping Dashboard</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            margin: 0;
            padding: 20px;
        }
        .dashboard {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #ffd700;
            margin-bottom: 10px;
        }
        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }
        .activities {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }
        .activity-item {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid #ff6b6b;
        }
        .activity-time {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        .refresh-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            margin: 20px auto;
            display: block;
        }
        .alert {
            background: #ff4757;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .success {
            background: #2ed573;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>🛡️ Anti-Scraping Protection Dashboard</h1>
            <p>Real-time monitoring and protection statistics</p>
        </div>
        
        <div id="alerts"></div>
        
        <div class="stats-grid" id="statsGrid">
            <!-- Stats will be loaded here -->
        </div>
        
        <div class="activities">
            <h3>🚨 Recent Suspicious Activities</h3>
            <div id="activitiesList">
                <!-- Activities will be loaded here -->
            </div>
        </div>
        
        <button class="refresh-btn" onclick="loadDashboard()">🔄 Refresh Data</button>
    </div>

    <script>
        async function loadDashboard() {
            try {
                const response = await fetch('/api/protection_report');
                const data = await response.json();
                
                updateStats(data.stats, data.protection_effectiveness);
                updateActivities(data.recent_activities);
                updateAlerts(data);
                
            } catch (error) {
                console.error('Failed to load dashboard:', error);
            }
        }
        
        function updateStats(stats, effectiveness) {
            const statsGrid = document.getElementById('statsGrid');
            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${stats.total_requests}</div>
                    <div class="stat-label">Total Requests</div>
                </div>

                <div class="stat-card">
                    <div class="stat-number">${stats.devtools_detections}</div>
                    <div class="stat-label">DevTools Detections</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.automation_detections}</div>
                    <div class="stat-label">Automation Blocked</div>
                </div>

                <div class="stat-card">
                    <div class="stat-number">${effectiveness.detection_rate.toFixed(1)}%</div>
                    <div class="stat-label">Detection Rate</div>
                </div>
            `;
        }
        
        function updateActivities(activities) {
            const activitiesList = document.getElementById('activitiesList');
            
            if (activities.length === 0) {
                activitiesList.innerHTML = '<p>No suspicious activities detected.</p>';
                return;
            }
            
            activitiesList.innerHTML = activities.map(activity => `
                <div class="activity-item">
                    <strong>${activity.type.replace('_', ' ').toUpperCase()}</strong><br>
                    IP: ${activity.ip}<br>
                    ${activity.video_id ? `Video: ${activity.video_id}<br>` : ''}
                    <div class="activity-time">${new Date(activity.timestamp * 1000).toLocaleString()}</div>
                </div>
            `).join('');
        }
        
        function updateAlerts(data) {
            const alerts = document.getElementById('alerts');
            let alertsHtml = '';
            
            if (data.total_suspicious_activities > 0) {
                alertsHtml += `
                    <div class="alert">
                        ⚠️ ${data.total_suspicious_activities} suspicious activities detected
                    </div>
                `;
            } else {
                alertsHtml += `
                    <div class="success">
                        ✅ Protection systems operating normally
                    </div>
                `;
            }
            
            alerts.innerHTML = alertsHtml;
        }
        
        // Auto-refresh every 30 seconds
        setInterval(loadDashboard, 30000);
        
        // Load on page load
        loadDashboard();
    </script>
</body>
</html>
    """
    
    with open('templates/protection_dashboard.html', 'w') as f:
        f.write(dashboard_html)

if __name__ == '__main__':
    # Generate sample report
    monitor.log_request('*************', 'Mozilla/5.0...', '/video-abc123')
    monitor.log_devtools_detection('*************', 'abc123')
    monitor.log_automation_detection('*************', 'python-requests/2.25.1')
    
    report = monitor.save_report()
    print("Anti-scraping report generated:")
    print(json.dumps(report, indent=2))
    
    create_protection_dashboard()
    print("Protection dashboard created at templates/protection_dashboard.html")
