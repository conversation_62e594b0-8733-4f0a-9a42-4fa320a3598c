<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" width="64" height="64">
  <defs>
    <!-- Fire gradient -->
    <radialGradient id="fireGradient" cx="50%" cy="80%" r="60%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#FF6B35;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#FF1744;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8B0000;stop-opacity:1" />
    </radialGradient>
    
    <!-- Video gradient -->
    <linearGradient id="videoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f093fb;stop-opacity:1" />
    </linearGradient>
    
    <!-- Glow effect -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background circle with glow -->
  <circle cx="32" cy="32" r="30" fill="url(#videoGradient)" filter="url(#glow)" opacity="0.9"/>
  
  <!-- Video play button base -->
  <polygon points="24,20 24,44 44,32" fill="#FFFFFF" opacity="0.95"/>
  
  <!-- Fire flames overlay -->
  <path d="M20,45 Q22,35 25,40 Q28,30 30,38 Q33,25 35,35 Q38,20 40,32 Q43,15 45,28 Q47,25 48,35 Q50,30 52,40" 
        fill="url(#fireGradient)" opacity="0.8"/>
  
  <!-- Smaller fire details -->
  <path d="M22,42 Q24,38 26,41 Q28,35 30,39 Q32,32 34,37 Q36,28 38,34 Q40,25 42,31 Q44,28 46,35" 
        fill="#FFD700" opacity="0.6"/>
  
  <!-- Hot sparkles -->
  <circle cx="18" cy="25" r="1.5" fill="#FFD700" opacity="0.8">
    <animate attributeName="opacity" values="0.8;0.3;0.8" dur="1.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="48" cy="18" r="1" fill="#FF6B35" opacity="0.7">
    <animate attributeName="opacity" values="0.7;0.2;0.7" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="52" cy="48" r="1.2" fill="#FF1744" opacity="0.9">
    <animate attributeName="opacity" values="0.9;0.4;0.9" dur="1.2s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Sexy highlight on play button -->
  <polygon points="24,20 24,32 36,26" fill="#FFFFFF" opacity="0.4"/>
</svg>
