# 🎬 CDN Video Player with Anti-Scraping Protection
{Made Via AI }

## GIT
https://github.com/sorathedsky/player

## 🚀 Quick Start

```bash
./start_cdn.sh
```
Then visit: **http://localhost:5000**

## ✨ Features

- 📤 **Submit any video URL** and get short links like `localhost:5000/video-abc123`
- 🔗 **Share anywhere** - social media, chat, email
- ⏰ **Auto-expire** videos (1 day to 1 year, default 2 weeks)
- 🛡️ **Full anti-scraping protection** on all pages
- 📊 **Real-time monitoring** dashboard

## 🛡️ Anti-Scraping Protection

### **Complete Protection Coverage**
- ✅ **DevTools Detection** - Blocks access when dev tools open
- ✅ **Right-Click Disabled** - Prevents context menu access
- ✅ **Keyboard Shortcuts Blocked** - F12, Ctrl+U, Ctrl+S, etc.
- ✅ **Text Selection Disabled** - Blocks content copying
- ✅ **Video URL Obfuscation** - Hides real video sources
- ✅ **Bot Detection** - Detects automation tools and scrapers
- ✅ **Real-Time Monitoring** - Live threat detection and logging

## 📊 Monitoring Dashboard

**URL**: `http://localhost:5000/protection`

- 📈 Real-time request statistics
- 🔍 DevTools detection alerts
- 🤖 Bot detection logs
- 📊 Protection effectiveness rates

## 🔧 Test the Protection

Try these (they should be blocked):
- Right-click anywhere
- Press F12 or Ctrl+Shift+I
- Try Ctrl+U (view source)
- Select text to copy
- Use automation tools

## 📁 File Structure

```
cdn_player.py                    # Main application
db.py                           # Firebase database integration
anti_scraping_monitor.py         # Protection monitoring
start_cdn.sh                     # Startup script
player_cache.json               # Local cache (auto-created)
templates/
  ├── cdn_homepage.html          # Protected homepage
  ├── cdn_player.html            # Protected video player
  └── protection_dashboard.html  # Monitoring dashboard
venv/                           # Python virtual environment
```

## 🎉 Complete Protection Active!

- ✅ **Homepage**: http://localhost:5001 - PROTECTED
- ✅ **Video Player**: All video URLs - PROTECTED
- ✅ **Dashboard**: http://localhost:5001/protection - MONITORING

Your CDN video player now has **military-grade anti-scraping protection** across all pages while maintaining seamless functionality for legitimate users!
