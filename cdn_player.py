#!/usr/bin/env python3
"""
CDN Video Player - Allow users to submit video links and get short URLs
"""

from flask import Flask, render_template, request, jsonify, redirect, abort, Response, session
from flask_cors import CORS
import json
import os
import time
import hashlib
import secrets
import re
import hmac
import base64
import jwt
from datetime import datetime, timedelta
from urllib.parse import urlparse
import requests
from anti_scraping_monitor import monitor
from db import player_db, FIREBASE_CONFIG
import os
from dotenv import load_dotenv
import threading
import gc
from functools import lru_cache
import weakref

# Load environment variables
load_dotenv()

# Dashboard Local JSON Database
class DashboardDB:
    def __init__(self, db_file='dashboard_data.json'):
        self.db_file = db_file
        self.lock = threading.Lock()
        self.data = self.load_data()

    def load_data(self):
        """Load dashboard data from JSON file"""
        try:
            if os.path.exists(self.db_file):
                with open(self.db_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            print(f"❌ Error loading dashboard data: {e}")

        # Default structure
        return {
            'active_sessions': {},
            'video_activity': [],
            'ip_stats': {},
            'daily_stats': {},
            'last_updated': time.time()
        }

    def save_data(self):
        """Save dashboard data to JSON file"""
        try:
            with self.lock:
                self.data['last_updated'] = time.time()
                with open(self.db_file, 'w') as f:
                    json.dump(self.data, f, indent=2)
        except Exception as e:
            print(f"❌ Error saving dashboard data: {e}")

    def get_real_ip_address(self, request):
        """Get the real public IP address of the user"""
        # Check for forwarded headers (common in proxy/load balancer setups)
        forwarded_ips = [
            request.headers.get('X-Forwarded-For'),
            request.headers.get('X-Real-IP'),
            request.headers.get('CF-Connecting-IP'),  # Cloudflare
            request.headers.get('X-Client-IP'),
            request.headers.get('X-Forwarded'),
            request.headers.get('Forwarded-For'),
            request.headers.get('Forwarded')
        ]

        # Get the first non-empty forwarded IP
        for forwarded_ip in forwarded_ips:
            if forwarded_ip:
                # Handle comma-separated IPs (take the first one)
                ip = forwarded_ip.split(',')[0].strip()
                if ip and ip not in ['127.0.0.1', 'localhost', '::1']:
                    return ip

        # If no forwarded IP or it's local, try to get public IP via external service
        client_ip = request.remote_addr
        if client_ip in ['127.0.0.1', 'localhost', '::1']:
            try:
                # Get public IP from external service
                response = requests.get('https://api.ipify.org?format=json', timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    public_ip = data.get('ip')
                    if public_ip:
                        print(f"🌐 Detected public IP: {public_ip} (was local: {client_ip})")
                        return public_ip
            except Exception as e:
                print(f"❌ Error getting public IP: {e}")

        return client_ip

    def get_ip_geolocation(self, ip_address):
        """Get IP geolocation information"""
        try:
            # Use ip-api.com for free geolocation
            response = requests.get(f'http://ip-api.com/json/{ip_address}?fields=status,message,country,regionName,city,timezone,isp,org,lat,lon,query', timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    return {
                        'country': data.get('country', 'Unknown'),
                        'region': data.get('regionName', 'Unknown'),
                        'city': data.get('city', 'Unknown'),
                        'timezone': data.get('timezone', 'Unknown'),
                        'isp': data.get('isp', 'Unknown'),
                        'org': data.get('org', 'Unknown'),
                        'lat': data.get('lat', 0.0),
                        'lon': data.get('lon', 0.0)
                    }
        except Exception as e:
            print(f"❌ Error getting geolocation for {ip_address}: {e}")

        return {
            'country': 'Unknown',
            'region': 'Unknown',
            'city': 'Unknown',
            'timezone': 'Unknown',
            'isp': 'Unknown',
            'org': 'Unknown',
            'lat': 0.0,
            'lon': 0.0
        }

    def parse_user_agent(self, user_agent):
        """Parse user agent for device information"""
        try:
            ua_lower = user_agent.lower()

            # Detect OS
            if 'windows' in ua_lower:
                if 'windows nt 10' in ua_lower:
                    os_name = 'Windows 10/11'
                elif 'windows nt 6.3' in ua_lower:
                    os_name = 'Windows 8.1'
                elif 'windows nt 6.1' in ua_lower:
                    os_name = 'Windows 7'
                else:
                    os_name = 'Windows'
            elif 'mac os x' in ua_lower or 'macos' in ua_lower:
                os_name = 'macOS'
            elif 'android' in ua_lower:
                os_name = 'Android'
            elif 'iphone' in ua_lower or 'ipad' in ua_lower:
                os_name = 'iOS'
            elif 'linux' in ua_lower:
                os_name = 'Linux'
            else:
                os_name = 'Unknown'

            # Detect Browser
            if 'chrome' in ua_lower and 'edg' not in ua_lower:
                browser = 'Chrome'
            elif 'firefox' in ua_lower:
                browser = 'Firefox'
            elif 'safari' in ua_lower and 'chrome' not in ua_lower:
                browser = 'Safari'
            elif 'edg' in ua_lower:
                browser = 'Edge'
            elif 'opera' in ua_lower:
                browser = 'Opera'
            else:
                browser = 'Unknown'

            # Detect Device Type
            if 'mobile' in ua_lower or 'android' in ua_lower or 'iphone' in ua_lower:
                device_type = 'Mobile'
            elif 'tablet' in ua_lower or 'ipad' in ua_lower:
                device_type = 'Tablet'
            else:
                device_type = 'Desktop'

            return {
                'os': os_name,
                'browser': browser,
                'device_type': device_type,
                'is_mobile': device_type in ['Mobile', 'Tablet']
            }
        except Exception as e:
            print(f"❌ Error parsing user agent: {e}")
            return {
                'os': 'Unknown',
                'browser': 'Unknown',
                'device_type': 'Unknown',
                'is_mobile': False
            }

    def log_video_access(self, video_id, ip_address, user_agent, action='view', extra_data=None, request=None):
        """Log video access activity with enhanced tracking"""
        with self.lock:
            current_time = time.time()

            # Get real IP address if request is provided
            if request:
                real_ip = self.get_real_ip_address(request)
                if real_ip != ip_address:
                    print(f"🌐 Using real IP {real_ip} instead of {ip_address}")
                    ip_address = real_ip

            # Get geolocation data
            geo_data = self.get_ip_geolocation(ip_address)

            # Parse user agent
            device_info = self.parse_user_agent(user_agent)

            # Add to activity log
            activity = {
                'timestamp': current_time,
                'video_id': video_id,
                'ip_address': ip_address,
                'user_agent': user_agent,
                'action': action,
                'geo_data': geo_data,
                'device_info': device_info,
                'extra_data': extra_data or {}
            }

            self.data['video_activity'].append(activity)

            # Keep only last 1000 activities
            if len(self.data['video_activity']) > 1000:
                self.data['video_activity'] = self.data['video_activity'][-1000:]

            # Update IP stats with enhanced tracking
            if ip_address not in self.data['ip_stats']:
                self.data['ip_stats'][ip_address] = {
                    'total_views': 0,
                    'videos_watched': [],
                    'first_seen': current_time,
                    'last_seen': current_time,
                    'user_agent': user_agent,
                    'geo_data': geo_data,
                    'device_info': device_info,
                    'session_count': 0,
                    'total_watch_time': 0,
                    'browsers_used': [],
                    'devices_used': [],
                    'actions': {'page_load': 0, 'video_play': 0, 'video_pause': 0, 'video_seek': 0}
                }

            # Ensure videos_watched is a list
            if isinstance(self.data['ip_stats'][ip_address]['videos_watched'], set):
                self.data['ip_stats'][ip_address]['videos_watched'] = list(self.data['ip_stats'][ip_address]['videos_watched'])

            # Update stats
            self.data['ip_stats'][ip_address]['total_views'] += 1
            if video_id not in self.data['ip_stats'][ip_address]['videos_watched']:
                self.data['ip_stats'][ip_address]['videos_watched'].append(video_id)
            self.data['ip_stats'][ip_address]['last_seen'] = current_time
            self.data['ip_stats'][ip_address]['user_agent'] = user_agent

            # Update geo data if changed or missing
            if 'geo_data' not in self.data['ip_stats'][ip_address] or self.data['ip_stats'][ip_address]['geo_data'].get('country') == 'Unknown':
                self.data['ip_stats'][ip_address]['geo_data'] = geo_data

            # Update device info
            self.data['ip_stats'][ip_address]['device_info'] = device_info

            # Track browsers and devices (with error handling for existing data)
            browser = device_info.get('browser', 'Unknown')
            device_type = device_info.get('device_type', 'Unknown')

            # Ensure browsers_used exists and is a list
            if 'browsers_used' not in self.data['ip_stats'][ip_address]:
                self.data['ip_stats'][ip_address]['browsers_used'] = []
            if browser not in self.data['ip_stats'][ip_address]['browsers_used']:
                self.data['ip_stats'][ip_address]['browsers_used'].append(browser)

            # Ensure devices_used exists and is a list
            if 'devices_used' not in self.data['ip_stats'][ip_address]:
                self.data['ip_stats'][ip_address]['devices_used'] = []
            if device_type not in self.data['ip_stats'][ip_address]['devices_used']:
                self.data['ip_stats'][ip_address]['devices_used'].append(device_type)

            # Track actions (with error handling for existing data)
            if 'actions' not in self.data['ip_stats'][ip_address]:
                self.data['ip_stats'][ip_address]['actions'] = {'page_load': 0, 'video_play': 0, 'video_pause': 0, 'video_seek': 0}
            if action in self.data['ip_stats'][ip_address]['actions']:
                self.data['ip_stats'][ip_address]['actions'][action] += 1
            else:
                self.data['ip_stats'][ip_address]['actions'][action] = 1

            # Update daily stats
            today = time.strftime('%Y-%m-%d', time.localtime(current_time))
            if today not in self.data['daily_stats']:
                self.data['daily_stats'][today] = {'views': 0, 'unique_ips': []}

            # Ensure unique_ips is a list
            if isinstance(self.data['daily_stats'][today]['unique_ips'], set):
                self.data['daily_stats'][today]['unique_ips'] = list(self.data['daily_stats'][today]['unique_ips'])

            self.data['daily_stats'][today]['views'] += 1
            if ip_address not in self.data['daily_stats'][today]['unique_ips']:
                self.data['daily_stats'][today]['unique_ips'].append(ip_address)

            # Store comprehensive user data if provided
            if extra_data:
                # Store the latest comprehensive data
                self.data['ip_stats'][ip_address]['comprehensive_data'] = extra_data

                # Extract and store specific useful data
                if 'battery' in extra_data and isinstance(extra_data['battery'], dict):
                    self.data['ip_stats'][ip_address]['latest_battery'] = extra_data['battery']

                if 'geolocation' in extra_data and isinstance(extra_data['geolocation'], dict):
                    self.data['ip_stats'][ip_address]['client_geolocation'] = extra_data['geolocation']

                if 'connection' in extra_data and isinstance(extra_data['connection'], dict):
                    self.data['ip_stats'][ip_address]['connection_info'] = extra_data['connection']

                if 'webrtc_ips' in extra_data:
                    self.data['ip_stats'][ip_address]['webrtc_ips'] = extra_data['webrtc_ips']

                if 'canvas_fingerprint' in extra_data:
                    self.data['ip_stats'][ip_address]['canvas_fingerprint'] = extra_data['canvas_fingerprint']

                if 'webgl' in extra_data and isinstance(extra_data['webgl'], dict):
                    self.data['ip_stats'][ip_address]['gpu_info'] = extra_data['webgl']

                if 'performance' in extra_data and isinstance(extra_data['performance'], dict):
                    # Store performance metrics
                    timing = extra_data['performance'].get('timing', {})
                    if isinstance(timing, dict) and 'navigationStart' in timing and 'loadEventEnd' in timing:
                        load_time = timing['loadEventEnd'] - timing['navigationStart']
                        if 'performance_metrics' not in self.data['ip_stats'][ip_address]:
                            self.data['ip_stats'][ip_address]['performance_metrics'] = []
                        self.data['ip_stats'][ip_address]['performance_metrics'].append({
                            'timestamp': current_time,
                            'load_time': load_time,
                            'action': action
                        })

                if 'storage' in extra_data and isinstance(extra_data['storage'], dict):
                    self.data['ip_stats'][ip_address]['storage_info'] = extra_data['storage']

        self.save_data()

    def update_active_session(self, video_id, ip_address, status='playing'):
        """Update active video sessions"""
        with self.lock:
            session_key = f"{ip_address}_{video_id}"
            self.data['active_sessions'][session_key] = {
                'video_id': video_id,
                'ip_address': ip_address,
                'status': status,
                'timestamp': time.time()
            }

            # Clean old sessions (older than 5 minutes)
            current_time = time.time()
            expired_sessions = []
            for key, session in self.data['active_sessions'].items():
                if current_time - session['timestamp'] > 300:  # 5 minutes
                    expired_sessions.append(key)

            for key in expired_sessions:
                del self.data['active_sessions'][key]

        self.save_data()

    def get_dashboard_stats(self):
        """Get comprehensive dashboard statistics"""
        with self.lock:
            current_time = time.time()

            # Recent activity (last 24 hours)
            recent_activity = [
                activity for activity in self.data['video_activity']
                if current_time - activity['timestamp'] < 86400
            ]

            # Active sessions
            active_sessions = []
            for session in self.data['active_sessions'].values():
                if current_time - session['timestamp'] < 300:  # 5 minutes
                    active_sessions.append(session)

            # Enhanced IP stats
            ip_stats = []
            country_stats = {}
            browser_stats = {}
            device_stats = {}
            os_stats = {}

            for ip, stats in self.data['ip_stats'].items():
                # Basic IP info
                ip_info = {
                    'ip': ip,
                    'total_views': stats['total_views'],
                    'unique_videos': len(stats['videos_watched']),
                    'last_seen': stats['last_seen'],
                    'first_seen': stats.get('first_seen', stats['last_seen']),
                    'user_agent': stats.get('user_agent', 'Unknown')[:100],
                    'geo_data': stats.get('geo_data', {}),
                    'device_info': stats.get('device_info', {}),
                    'browsers_used': stats.get('browsers_used', []),
                    'devices_used': stats.get('devices_used', []),
                    'actions': stats.get('actions', {}),
                    'session_count': stats.get('session_count', 0)
                }
                ip_stats.append(ip_info)

                # Country statistics
                country = stats.get('geo_data', {}).get('country', 'Unknown')
                if country not in country_stats:
                    country_stats[country] = {'count': 0, 'views': 0}
                country_stats[country]['count'] += 1
                country_stats[country]['views'] += stats['total_views']

                # Browser statistics
                browser = stats.get('device_info', {}).get('browser', 'Unknown')
                if browser not in browser_stats:
                    browser_stats[browser] = {'count': 0, 'views': 0}
                browser_stats[browser]['count'] += 1
                browser_stats[browser]['views'] += stats['total_views']

                # Device statistics
                device_type = stats.get('device_info', {}).get('device_type', 'Unknown')
                if device_type not in device_stats:
                    device_stats[device_type] = {'count': 0, 'views': 0}
                device_stats[device_type]['count'] += 1
                device_stats[device_type]['views'] += stats['total_views']

                # OS statistics
                os_name = stats.get('device_info', {}).get('os', 'Unknown')
                if os_name not in os_stats:
                    os_stats[os_name] = {'count': 0, 'views': 0}
                os_stats[os_name]['count'] += 1
                os_stats[os_name]['views'] += stats['total_views']

            ip_stats.sort(key=lambda x: x['total_views'], reverse=True)

            # Video statistics
            video_stats = {}
            for activity in self.data['video_activity']:
                video_id = activity['video_id']
                if video_id not in video_stats:
                    video_stats[video_id] = {
                        'total_views': 0,
                        'unique_ips': set(),
                        'countries': set(),
                        'devices': set(),
                        'last_viewed': 0
                    }

                video_stats[video_id]['total_views'] += 1
                video_stats[video_id]['unique_ips'].add(activity['ip_address'])
                video_stats[video_id]['last_viewed'] = max(video_stats[video_id]['last_viewed'], activity['timestamp'])

                if 'geo_data' in activity:
                    country = activity['geo_data'].get('country', 'Unknown')
                    video_stats[video_id]['countries'].add(country)

                if 'device_info' in activity:
                    device = activity['device_info'].get('device_type', 'Unknown')
                    video_stats[video_id]['devices'].add(device)

            # Convert sets to lists for JSON serialization
            for video_id in video_stats:
                video_stats[video_id]['unique_ips'] = len(video_stats[video_id]['unique_ips'])
                video_stats[video_id]['countries'] = list(video_stats[video_id]['countries'])
                video_stats[video_id]['devices'] = list(video_stats[video_id]['devices'])

            # Time-based statistics
            hourly_stats = {}
            daily_stats = {}

            for activity in recent_activity:
                # Hourly stats
                hour = time.strftime('%H:00', time.localtime(activity['timestamp']))
                if hour not in hourly_stats:
                    hourly_stats[hour] = 0
                hourly_stats[hour] += 1

                # Daily stats
                day = time.strftime('%Y-%m-%d', time.localtime(activity['timestamp']))
                if day not in daily_stats:
                    daily_stats[day] = {'views': 0, 'unique_ips': set()}
                daily_stats[day]['views'] += 1
                daily_stats[day]['unique_ips'].add(activity['ip_address'])

            # Convert sets to counts
            for day in daily_stats:
                daily_stats[day]['unique_ips'] = len(daily_stats[day]['unique_ips'])

            return {
                'active_sessions': active_sessions,
                'recent_activity': recent_activity[-50:],  # Last 50 activities
                'top_ips': ip_stats[:20],  # Top 20 IPs
                'total_unique_ips': len(self.data['ip_stats']),
                'total_activities': len(self.data['video_activity']),
                'country_stats': dict(sorted(country_stats.items(), key=lambda x: x[1]['views'], reverse=True)[:10]),
                'browser_stats': dict(sorted(browser_stats.items(), key=lambda x: x[1]['views'], reverse=True)),
                'device_stats': dict(sorted(device_stats.items(), key=lambda x: x[1]['views'], reverse=True)),
                'os_stats': dict(sorted(os_stats.items(), key=lambda x: x[1]['views'], reverse=True)),
                'video_stats': dict(sorted(video_stats.items(), key=lambda x: x[1]['total_views'], reverse=True)[:10]),
                'hourly_stats': hourly_stats,
                'daily_stats': daily_stats,
                'last_updated': self.data['last_updated']
            }

# Initialize dashboard database
dashboard_db = DashboardDB()

app = Flask(__name__)
CORS(app)
app.secret_key = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')  # For session management

# Redirect configuration
REDIR_ENABLED = os.getenv('REDIR', 'False').lower() == 'true'
REDIR_LINK = os.getenv('REDIR_LINK', 'https://t.me/Hentai_Aria')

# Serve hot sexy favicon
@app.route('/favicon.ico')
def favicon():
    return app.send_static_file('favicon.ico')

# Anti-scraping configuration
SECURITY_HEADERS = {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; media-src *; img-src 'self' data:;",
    'Referrer-Policy': 'no-referrer',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
}

def add_security_headers(response):
    """Add comprehensive security headers"""
    for header, value in SECURITY_HEADERS.items():
        response.headers[header] = value
    return response

def is_bot_request():
    """Enhanced bot detection with smart browser recognition"""
    user_agent = request.headers.get('User-Agent', '').lower()

    # First, check if it's a legitimate browser
    legitimate_browsers = ['mozilla', 'chrome', 'firefox', 'safari', 'edge', 'opera']
    is_browser = any(browser in user_agent for browser in legitimate_browsers)

    # If it's a browser, be more lenient
    if is_browser:
        # Only block obvious automation tools even in browsers
        automation_patterns = ['selenium', 'webdriver', 'phantomjs', 'headless', 'automation']
        if any(pattern in user_agent for pattern in automation_patterns):
            return True
        # Allow browsers to access streaming without referer requirement
        return False

    # For non-browser user agents, be more strict
    bot_patterns = [
        'bot', 'crawler', 'spider', 'scraper', 'wget', 'curl',
        'python', 'requests', 'urllib', 'scrapy', 'postman',
        'insomnia', 'httpie', 'axios', 'fetch', 'node', 'go-http',
        'java', 'okhttp', 'apache', 'libwww', 'winhttp', 'cfnetwork',
        'download', 'monitor', 'check', 'scan', 'probe', 'validator'
    ]

    # Check user agent for bot patterns
    if any(pattern in user_agent for pattern in bot_patterns):
        # Allow legitimate search engine bots
        if not any(allowed in user_agent for allowed in ['googlebot', 'bingbot', 'slurp']):
            return True

    # Check for empty or very short user agents
    if not user_agent or len(user_agent) < 10:
        return True

    # For streaming endpoints from non-browsers, require proper referer
    if request.endpoint == 'stream_video':
        referer = request.headers.get('Referer', '')
        if not referer or request.host not in referer:
            return True

    return False

@app.before_request
def before_request():
    """Lightweight request preprocessing - optimized for low CPU/RAM"""
    # Skip all heavy processing for static files and API stats
    if request.endpoint in ['static'] or request.path.endswith(('.css', '.js', '.png', '.jpg')) or request.path == '/api/stats':
        return None

    # Minimal bot blocking - only block obvious curl/wget
    user_agent = request.headers.get('User-Agent', '').lower()
    if any(bot in user_agent for bot in ['curl', 'wget', 'python-requests']):
        print(f"BLOCKED bot: {user_agent[:50]}")
        abort(403)

    # Minimal session tracking (no rate limiting to save CPU)
    session_id = user_manager.track_user(request)

@app.after_request
def after_request(response):
    """Lightweight response post-processing - optimized for low CPU/RAM"""
    # Minimal security headers only
    response.headers.update({
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
    })

    # Cache static files only
    if request.path.endswith(('.css', '.js', '.png', '.jpg')):
        response.cache_control.max_age = 3600

    return response

# Template filters
@app.template_filter('timestamp_to_date')
def timestamp_to_date(timestamp):
    """Convert timestamp to readable date"""
    try:
        return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M')
    except:
        return 'Unknown'

# Configuration
MAX_VIDEO_SIZE_MB = 500
ALLOWED_DOMAINS = ['youtube.com', 'youtu.be', 'vimeo.com', 'dailymotion.com', 'twitch.tv', 'imgur.com', 'giphy.com', 'drive.google.com', 'docs.google.com']
DEFAULT_EXPIRE_DAYS = 30
SECRET_KEY = secrets.token_hex(32)  # For token generation
JWT_SECRET = os.getenv('JWT_SECRET', secrets.token_hex(32))  # JWT secret for masked URLs
TOKEN_EXPIRY_MINUTES = 60  # Token expires in 1 hour

# Google Drive configuration
DRIVE_API_KEY = os.getenv('DRIVE_API', 'AIzaSyAd2xbht3di_oA1nBhcme0LxiKAtPYH-vs')
DRIVE_STREAM_URL_TEMPLATE = os.getenv('DRIVE_STREAM_URL', 'https://www.googleapis.com/drive/v3/files/{fileid}?alt=media&key={APIKey}')

# Multi-user optimization settings
MAX_CONCURRENT_USERS = 100
SESSION_TIMEOUT = 1800  # 30 minutes
MEMORY_CLEANUP_INTERVAL = 300  # 5 minutes
MAX_CACHE_SIZE = 50  # Maximum cached items per type

class MultiUserManager:
    """Lightweight multi-user session manager with memory optimization"""

    def __init__(self):
        self.active_sessions = weakref.WeakValueDictionary()
        self.session_data = {}
        self.last_cleanup = time.time()
        self.lock = threading.RLock()

    def get_session_id(self, request):
        """Generate or retrieve session ID for user"""
        user_ip = request.remote_addr
        user_agent = request.headers.get('User-Agent', '')[:50]  # Limit length
        session_key = hashlib.md5(f"{user_ip}:{user_agent}".encode()).hexdigest()[:16]
        return session_key

    def track_user(self, request):
        """Track user session with minimal memory footprint"""
        session_id = self.get_session_id(request)
        current_time = time.time()

        with self.lock:
            # Cleanup old sessions periodically
            if current_time - self.last_cleanup > MEMORY_CLEANUP_INTERVAL:
                self.cleanup_sessions()
                self.last_cleanup = current_time

            # Update session
            self.session_data[session_id] = {
                'last_seen': current_time,
                'ip': request.remote_addr,
                'requests': self.session_data.get(session_id, {}).get('requests', 0) + 1
            }

            return session_id

    def cleanup_sessions(self):
        """Remove expired sessions and force garbage collection"""
        current_time = time.time()
        expired_sessions = []

        for session_id, data in self.session_data.items():
            if current_time - data['last_seen'] > SESSION_TIMEOUT:
                expired_sessions.append(session_id)

        for session_id in expired_sessions:
            del self.session_data[session_id]

        # Force garbage collection
        gc.collect()

        if expired_sessions:
            print(f"🧹 Cleaned {len(expired_sessions)} expired sessions")

    def get_active_users(self):
        """Get count of active users"""
        current_time = time.time()
        active_count = sum(1 for data in self.session_data.values()
                          if current_time - data['last_seen'] < SESSION_TIMEOUT)
        return active_count

    def is_rate_limited(self, session_id, max_requests_per_minute=60):
        """Simple rate limiting per session"""
        if session_id not in self.session_data:
            return False

        session = self.session_data[session_id]
        current_time = time.time()

        # Reset counter every minute
        if current_time - session.get('rate_limit_reset', 0) > 60:
            session['rate_limit_count'] = 0
            session['rate_limit_reset'] = current_time

        session['rate_limit_count'] = session.get('rate_limit_count', 0) + 1
        return session['rate_limit_count'] > max_requests_per_minute

# Global multi-user manager
user_manager = MultiUserManager()

# Memory-efficient caching
@lru_cache(maxsize=MAX_CACHE_SIZE)
def cached_video_validation(url_hash):
    """Cache video URL validation results"""
    return True  # Placeholder for validation logic

@lru_cache(maxsize=MAX_CACHE_SIZE)
def cached_drive_file_id(url_hash):
    """Cache Google Drive file ID extraction"""
    # This will be populated by the actual extraction function
    return None

def clear_caches():
    """Clear all LRU caches to free memory"""
    cached_video_validation.cache_clear()
    cached_drive_file_id.cache_clear()
    gc.collect()

# Memory optimization middleware
def optimize_memory():
    """Periodic memory optimization"""
    def memory_worker():
        while True:
            time.sleep(MEMORY_CLEANUP_INTERVAL)
            user_manager.cleanup_sessions()
            clear_caches()
            gc.collect()

    memory_thread = threading.Thread(target=memory_worker, daemon=True)
    memory_thread.start()
    print("🧠 Memory optimization started")

# Start memory optimization
optimize_memory()

def load_cdn_data():
    """Load CDN video data from Firebase with local cache"""
    return player_db.get_all_videos()

def save_cdn_data(data):
    """Save CDN video data to Firebase (deprecated - use individual video saves)"""
    # This function is kept for compatibility but individual saves are preferred
    for video_id, video_data in data.items():
        player_db.save_video(video_id, video_data)

def generate_short_id():
    """Generate a short unique ID for video"""
    return secrets.token_urlsafe(6).replace('-', '').replace('_', '')[:6].lower()

def generate_video_token(video_id, expiry_minutes=TOKEN_EXPIRY_MINUTES):
    """Generate a secure token for video access"""
    timestamp = int(time.time() + (expiry_minutes * 60))
    message = f"{video_id}:{timestamp}"
    signature = hmac.new(
        SECRET_KEY.encode(),
        message.encode(),
        hashlib.sha256
    ).hexdigest()
    token = base64.b64encode(f"{message}:{signature}".encode()).decode()
    return token

def generate_jwt_masked_url(video_url, video_id, expiry_hours=1):
    """Generate JWT-based masked URL with 1-hour expiry"""
    try:
        from datetime import timezone
        payload = {
            'video_id': video_id,
            'video_url': video_url,
            'iat': datetime.now(timezone.utc),
            'exp': datetime.now(timezone.utc) + timedelta(hours=expiry_hours),
            'iss': 'cdn-player',
            'aud': 'video-stream'
        }

        token = jwt.encode(payload, JWT_SECRET, algorithm='HS256')
        masked_url = f"/stream/secure/{token}"

        print(f"🔒 Generated JWT masked URL for {video_id}: {masked_url[:50]}...")
        return masked_url

    except Exception as e:
        print(f"❌ Error generating JWT masked URL: {e}")
        return video_url  # Fallback to original URL

def verify_jwt_token(token):
    """Lightweight JWT verification - optimized for low CPU"""
    try:
        # Simple decode with minimal verification for performance
        payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'], options={"verify_exp": False, "verify_aud": False})
        return payload
    except Exception:
        # Fallback: allow any valid JWT structure
        try:
            payload = jwt.decode(token, options={"verify_signature": False})
            return payload
        except Exception:
            return None

def validate_video_token(token, video_id):
    """Validate a video access token"""
    try:
        decoded = base64.b64decode(token.encode()).decode()
        parts = decoded.split(':')
        if len(parts) != 3:
            return False

        token_video_id, timestamp_str, signature = parts
        timestamp = int(timestamp_str)

        # Check if token is for the correct video
        if token_video_id != video_id:
            return False

        # Check if token has expired
        if time.time() > timestamp:
            return False

        # Verify signature
        message = f"{token_video_id}:{timestamp_str}"
        expected_signature = hmac.new(
            SECRET_KEY.encode(),
            message.encode(),
            hashlib.sha256
        ).hexdigest()

        return hmac.compare_digest(signature, expected_signature)
    except:
        return False

def extract_google_drive_file_id(url):
    """Extract file ID from Google Drive URL"""
    try:
        # Handle different Google Drive URL formats
        patterns = [
            r'/file/d/([a-zA-Z0-9-_]+)',  # https://drive.google.com/file/d/FILE_ID/view
            r'id=([a-zA-Z0-9-_]+)',       # https://drive.google.com/open?id=FILE_ID
            r'/d/([a-zA-Z0-9-_]+)',       # https://docs.google.com/document/d/FILE_ID
        ]

        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)

        return None
    except:
        return None

def get_google_drive_stream_url(file_id):
    """Convert Google Drive file ID to direct stream URL"""
    return DRIVE_STREAM_URL_TEMPLATE.format(fileid=file_id, APIKey=DRIVE_API_KEY)

def is_google_drive_url(url):
    """Check if URL is a Google Drive URL"""
    return any(domain in url.lower() for domain in ['drive.google.com', 'docs.google.com'])

def detect_video_platform(url):
    """Detect video platform and return platform info with direct streaming URL"""
    url_lower = url.lower()

    # Google Drive
    if is_google_drive_url(url):
        file_id = extract_google_drive_file_id(url)
        if file_id:
            return {
                'platform': 'google_drive',
                'file_id': file_id,
                'direct_url': get_google_drive_stream_url(file_id),
                'needs_processing': True,
                'supports_seeking': True
            }

    # Catbox.moe - Direct file hosting
    elif 'catbox.moe' in url_lower:
        if 'files.catbox.moe' in url_lower:
            return {
                'platform': 'catbox',
                'direct_url': url,
                'needs_processing': False,
                'supports_seeking': True
            }
        elif 'catbox.moe/v/' in url_lower:
            # Convert catbox.moe/v/xxx to files.catbox.moe/xxx
            file_id = url_lower.split('/v/')[-1].split('?')[0]
            return {
                'platform': 'catbox',
                'direct_url': f"https://files.catbox.moe/{file_id}",
                'needs_processing': False,
                'supports_seeking': True
            }

    # Imgur - Image and video hosting
    elif 'imgur.com' in url_lower:
        if 'i.imgur.com' in url_lower:
            return {
                'platform': 'imgur',
                'direct_url': url,
                'needs_processing': False,
                'supports_seeking': True
            }
        elif 'imgur.com/' in url_lower:
            # Convert imgur.com/xxx to i.imgur.com/xxx.mp4
            file_id = url_lower.split('imgur.com/')[-1].split('?')[0].split('.')[0]
            # Try multiple formats
            for ext in ['.mp4', '.webm', '.gif']:
                test_url = f"https://i.imgur.com/{file_id}{ext}"
                return {
                    'platform': 'imgur',
                    'direct_url': test_url,
                    'needs_processing': False,
                    'supports_seeking': True
                }

    # Streamable
    elif 'streamable.com' in url_lower:
        video_id = url_lower.split('streamable.com/')[-1].split('?')[0]
        return {
            'platform': 'streamable',
            'direct_url': f"https://cdn-cf-east.streamable.com/video/mp4/{video_id}.mp4",
            'needs_processing': False,
            'supports_seeking': True
        }

    # Gfycat
    elif 'gfycat.com' in url_lower:
        video_id = url_lower.split('gfycat.com/')[-1].split('?')[0]
        return {
            'platform': 'gfycat',
            'direct_url': f"https://giant.gfycat.com/{video_id}.mp4",
            'needs_processing': False,
            'supports_seeking': True
        }

    # Reddit video
    elif 'v.redd.it' in url_lower:
        base_url = url.rstrip('/')
        return {
            'platform': 'reddit',
            'direct_url': f"{base_url}/DASH_720.mp4" if not url.endswith('.mp4') else url,
            'needs_processing': False,
            'supports_seeking': True
        }

    # Discord CDN
    elif 'cdn.discordapp.com' in url_lower or 'media.discordapp.net' in url_lower:
        return {
            'platform': 'discord',
            'direct_url': url,
            'needs_processing': False,
            'supports_seeking': True
        }

    # Direct video files
    elif any(ext in url_lower for ext in ['.mp4', '.webm', '.avi', '.mov', '.mkv', '.m4v', '.flv', '.wmv']):
        return {
            'platform': 'direct',
            'direct_url': url,
            'needs_processing': False,
            'supports_seeking': True
        }

    # Default fallback - try as direct URL
    return {
        'platform': 'unknown',
        'direct_url': url,
        'needs_processing': False,
        'supports_seeking': False
    }

def process_google_drive_url(url):
    """Process Google Drive URL and return streamable URL"""
    file_id = extract_google_drive_file_id(url)
    if file_id:
        stream_url = get_google_drive_stream_url(file_id)
        print(f"🔗 Google Drive URL processed: {file_id} -> {stream_url}")
        return stream_url
    return None

def is_valid_video_url(url):
    """Check if URL is a valid video URL using enhanced platform detection"""
    try:
        parsed = urlparse(url)
        if not parsed.scheme or not parsed.netloc:
            return False

        # Use platform detection for validation
        platform_info = detect_video_platform(url)

        # Supported platforms
        supported_platforms = [
            'google_drive', 'catbox', 'imgur', 'streamable',
            'gfycat', 'reddit', 'discord', 'direct'
        ]

        if platform_info['platform'] in supported_platforms:
            print(f"✅ Valid {platform_info['platform']} URL detected")
            return True

        # Check file extension for direct video files
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.webm', '.gif', '.m4v', '.flv', '.wmv']
        if any(url.lower().endswith(ext) for ext in video_extensions):
            print(f"✅ Valid direct video file detected")
            return True

        # Check allowed domains (fallback)
        domain = parsed.netloc.lower()
        allowed_domains = [
            'drive.google.com', 'docs.google.com',
            'catbox.moe', 'files.catbox.moe',
            'imgur.com', 'i.imgur.com',
            'streamable.com', 'cdn-cf-east.streamable.com',
            'gfycat.com', 'giant.gfycat.com',
            'v.redd.it', 'reddit.com',
            'cdn.discordapp.com', 'media.discordapp.net'
        ]

        if any(allowed in domain for allowed in allowed_domains):
            print(f"✅ Valid domain detected: {domain}")
            return True

        # Unknown platform - allow but warn
        print(f"⚠️ Unknown platform, allowing: {domain}")
        return True  # Be permissive for unknown platforms

    except Exception as e:
        print(f"❌ URL validation error: {e}")
        return False

def check_video_accessibility(url):
    """Check if video URL is accessible"""
    try:
        response = requests.head(url, timeout=10, allow_redirects=True)
        return response.status_code == 200
    except:
        return False

def refresh_google_drive_url(video_data):
    """Refresh Google Drive stream URL if needed"""
    if video_data.get('is_google_drive', False):
        original_url = video_data.get('original_url', '')
        if original_url:
            new_stream_url = process_google_drive_url(original_url)
            if new_stream_url:
                video_data['url'] = new_stream_url
                print(f"🔄 Google Drive URL refreshed for video")
                return True
    return False

def clean_expired_videos():
    """Remove expired videos from Firebase and cache"""
    return player_db.cleanup_expired_videos()

@app.route('/')
def homepage():
    """CDN Player Homepage"""
    return render_template('cdn_homepage.html')

@app.route('/api/submit_video', methods=['POST'])
def submit_video():
    """Submit a video URL and get a short link"""
    try:
        data = request.get_json()
        video_url = data.get('url', '').strip()
        custom_expire = data.get('expire_days')
        title = data.get('title', '').strip()
        
        if not video_url:
            return jsonify({'error': 'Video URL is required'}), 400
        
        # Validate URL
        if not is_valid_video_url(video_url):
            return jsonify({'error': 'Invalid video URL or unsupported domain'}), 400

        # Detect and process video platform
        original_url = video_url
        platform_info = detect_video_platform(video_url)

        if platform_info['platform'] == 'google_drive':
            if platform_info.get('direct_url'):
                video_url = platform_info['direct_url']
                print(f"📁 Google Drive URL converted: {original_url} -> {video_url}")
            else:
                return jsonify({'error': 'Could not process Google Drive URL'}), 400
        elif platform_info['platform'] in ['catbox', 'imgur', 'streamable', 'gfycat', 'reddit', 'discord', 'direct']:
            video_url = platform_info['direct_url']
            print(f"🎬 {platform_info['platform'].title()} URL processed: {original_url} -> {video_url}")
        elif platform_info['platform'] == 'unknown':
            print(f"⚠️ Unknown platform, trying direct access: {video_url}")
        else:
            video_url = platform_info['direct_url']

        # Check accessibility (optional, can be slow)
        if not check_video_accessibility(video_url):
            return jsonify({'error': 'Video URL is not accessible'}), 400
        
        # Generate short ID
        video_id = generate_short_id()

        # Ensure unique ID
        cdn_data = load_cdn_data()
        while video_id in cdn_data:
            video_id = generate_short_id()

        # Calculate expiry
        expire_days = custom_expire if custom_expire and 1 <= custom_expire <= 365 else DEFAULT_EXPIRE_DAYS
        expires_at = time.time() + (expire_days * 24 * 60 * 60)

        # Store video data with platform information
        video_data = {
            'url': video_url,
            'original_url': original_url,  # Store original URL for reference
            'title': title or f'Video {video_id}',
            'created_at': time.time(),
            'expires_at': expires_at,
            'expire_days': expire_days,
            'views': 0,
            'last_viewed': None,
            'creator_ip': request.remote_addr,
            'platform': platform_info['platform'],
            'supports_seeking': platform_info.get('supports_seeking', False),
            'is_google_drive': platform_info['platform'] == 'google_drive'
        }

        # Save to Firebase immediately
        save_success = player_db.save_video(video_id, video_data)
        if save_success:
            print(f"✅ Video {video_id} saved to Firebase successfully")

            # Verify it was saved by retrieving it
            verification = player_db.get_video(video_id)
            if verification:
                print(f"✅ Video {video_id} verified in Firebase")
            else:
                print(f"❌ Video {video_id} save verification failed!")
        else:
            print(f"❌ Failed to save video {video_id} to Firebase")
            return jsonify({'error': 'Failed to save video to database'}), 500

        # Generate short URL
        short_url = f"{request.host_url}video-{video_id}"

        return jsonify({
            'success': True,
            'video_id': video_id,
            'short_url': short_url,
            'expires_at': datetime.fromtimestamp(expires_at).isoformat(),
            'expire_days': expire_days,
            'firebase_saved': save_success
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/video-<video_id>')
def play_video(video_id):
    """Play video from short URL with anti-scraping protection"""
    print(f"🔍 Attempting to load video: {video_id}")

    # Log dashboard activity with real IP detection
    client_ip = request.remote_addr
    user_agent = request.headers.get('User-Agent', 'Unknown')
    dashboard_db.log_video_access(video_id, client_ip, user_agent, 'page_load', request=request)

    # Get video data directly from Firebase
    video_data = player_db.get_video(video_id)

    if not video_data:
        print(f"❌ Video {video_id} not found in Firebase")
        # Let's also check what videos exist
        all_videos = player_db.get_all_videos()
        print(f"📋 Available videos: {list(all_videos.keys())}")
        abort(404)

    print(f"✅ Video {video_id} found: {video_data.get('title', 'No title')}")

    # Check if expired
    current_time = time.time()
    expires_at = video_data.get('expires_at', 0)
    if current_time > expires_at:
        print(f"⏰ Video {video_id} expired (current: {current_time}, expires: {expires_at})")
        # Remove expired video
        player_db.delete_video(video_id)
        abort(410)  # Gone

    print(f"🔒 Loading video player for {video_id} (URL will be fetched dynamically)")

    return render_template('cdn_player.html',
                         video_data=video_data,
                         video_id=video_id,
                         redir_enabled=REDIR_ENABLED,
                         redir_link=REDIR_LINK)

@app.route('/api/video_info/<video_id>')
def get_video_info(video_id):
    """Get video information"""
    video_data = player_db.get_video(video_id)

    if not video_data:
        return jsonify({'error': 'Video not found'}), 404

    # Check if expired
    if time.time() > video_data.get('expires_at', 0):
        return jsonify({'error': 'Video expired'}), 410

    # Return safe info (without creator IP)
    safe_data = {
        'title': video_data['title'],
        'created_at': video_data['created_at'],
        'expires_at': video_data['expires_at'],
        'views': video_data['views'],
        'last_viewed': video_data.get('last_viewed')
    }

    return jsonify(safe_data)

@app.route('/api/cleanup')
def cleanup_expired():
    """Manual cleanup of expired videos"""
    cleaned = clean_expired_videos()
    return jsonify({'cleaned': cleaned, 'message': f'Removed {cleaned} expired videos'})

@app.route('/api/get_secure_url/<video_id>')
def get_secure_url(video_id):
    """Get secure video URL dynamically"""
    try:
        # Basic bot protection
        user_agent = request.headers.get('User-Agent', '')
        if not user_agent or 'curl' in user_agent.lower() or 'wget' in user_agent.lower():
            abort(403)

        cdn_data = load_cdn_data()
        if video_id not in cdn_data:
            return jsonify({'error': 'Video not found'}), 404

        video_data = cdn_data[video_id]
        current_time = time.time()

        if current_time > video_data.get('expires_at', 0):
            return jsonify({'error': 'Video expired'}), 410

        # Generate secure token
        secure_url = generate_jwt_masked_url(video_data['url'], video_id, expiry_hours=1)

        return jsonify({
            'secure_url': secure_url,
            'expires_in': 3600  # 1 hour
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/count_view/<video_id>', methods=['POST'])
def count_view(video_id):
    """Count view with session-based tracking (1 view per session per video)"""
    try:
        # Basic bot protection
        user_agent = request.headers.get('User-Agent', '')
        if not user_agent or 'curl' in user_agent.lower() or 'wget' in user_agent.lower():
            abort(403)

        # Get video data from Firebase
        video_data = player_db.get_video(video_id)
        if not video_data:
            return jsonify({'error': 'Video not found'}), 404

        # Check if video has expired
        current_time = time.time()
        if current_time > video_data.get('expires_at', 0):
            return jsonify({'error': 'Video expired'}), 410

        # Session-based view counting with real IP
        client_ip = request.remote_addr
        real_ip = dashboard_db.get_real_ip_address(request)
        session_id = request.headers.get('X-Session-ID', f"{real_ip}_{user_agent}")

        # Get comprehensive user information
        user_info_header = request.headers.get('X-User-Info')
        extra_data = {}
        if user_info_header:
            try:
                extra_data = json.loads(user_info_header)
                print(f"📊 Comprehensive user info received: {len(str(extra_data))} chars")

                # Log specific interesting data
                if 'battery' in extra_data and isinstance(extra_data['battery'], dict):
                    battery = extra_data['battery']
                    print(f"🔋 Battery: {battery.get('level', 'Unknown')}% {'⚡' if battery.get('charging') else '🔋'}")

                if 'geolocation' in extra_data and isinstance(extra_data['geolocation'], dict):
                    geo = extra_data['geolocation']
                    print(f"📍 Geolocation: {geo.get('latitude', 'Unknown')}, {geo.get('longitude', 'Unknown')} (±{geo.get('accuracy', 'Unknown')}m)")

                if 'connection' in extra_data and isinstance(extra_data['connection'], dict):
                    conn = extra_data['connection']
                    print(f"🌐 Connection: {conn.get('effectiveType', 'Unknown')} RTT:{conn.get('rtt', 'Unknown')}ms")

                if 'webrtc_ips' in extra_data:
                    print(f"🔗 WebRTC IPs: {extra_data['webrtc_ips']}")

            except Exception as e:
                print(f"❌ Error parsing user info: {e}")

        # Log dashboard activity with comprehensive data and real IP detection
        dashboard_db.log_video_access(video_id, client_ip, user_agent, 'video_play', extra_data, request=request)

        # Get real IP for session tracking
        real_ip = dashboard_db.get_real_ip_address(request)
        dashboard_db.update_active_session(video_id, real_ip, 'playing')

        # Use session-based view counting
        threading.Thread(
            target=player_db.update_video_views_per_session,
            args=(video_id, session_id),
            daemon=True
        ).start()

        print(f"📊 Session-based view count triggered for video {video_id} from session: {session_id[:20]}...")

        return jsonify({'status': 'success', 'message': 'View counted'})

    except Exception as e:
        print(f"❌ Error counting view for {video_id}: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats')
def get_stats():
    """Get CDN player statistics with Firebase integration"""
    # Get stats from Firebase
    stats = player_db.get_stats()
    cdn_data = load_cdn_data()
    current_time = time.time()

    total_videos = len(cdn_data)
    active_videos = sum(1 for v in cdn_data.values() if current_time <= v.get('expires_at', 0))
    expired_videos = total_videos - active_videos
    total_views = sum(v.get('views', 0) for v in cdn_data.values())

    # Calculate recent videos
    recent_videos = []
    sorted_videos = sorted(cdn_data.items(), key=lambda x: x[1].get('created_at', 0), reverse=True)
    for video_id, video_data in sorted_videos[:5]:
        recent_videos.append({
            'id': video_id,
            'title': video_data['title'],
            'views': video_data['views'],
            'created_at': video_data['created_at']
        })

    return jsonify({
        'total_videos': total_videos,
        'active_videos': active_videos,
        'expired_videos': expired_videos,
        'total_views': total_views,
        'recent_videos': recent_videos,
        'firebase_connected': player_db.connected,
        'last_sync': stats.get('last_updated', time.time())
    })

@app.route('/api/protection_report')
def get_protection_report():
    """Get anti-scraping protection report"""
    return jsonify(monitor.get_protection_report())

@app.route('/api/database_status')
def get_database_status():
    """Get database connection and status information"""
    try:
        # Get detailed status from database
        status = player_db.get_connection_status()

        # Test database operations
        test_data = player_db.get_all_videos()
        can_read = len(test_data) >= 0

        return jsonify({
            'firebase_connected': status['firebase_connected'],
            'firebase_videos_count': status['firebase_videos'],
            'mode': status['mode'],
            'cache_disabled': status['cache_disabled'],
            'can_read_data': can_read,
            'total_videos_accessible': len(test_data),
            'stats': status['stats'],
            'firebase_config': {
                'project_id': FIREBASE_CONFIG.get('projectId', 'unknown'),
                'database_url': FIREBASE_CONFIG.get('databaseURL', 'unknown')
            }
        })
    except Exception as e:
        return jsonify({
            'error': str(e),
            'firebase_connected': False,
            'firebase_videos_count': 0
        }), 500

@app.route('/api/database_refresh', methods=['POST'])
def refresh_database():
    """Force refresh database from Firebase"""
    try:
        success = player_db.force_refresh_from_firebase()
        if success:
            videos = player_db.get_all_videos()
            return jsonify({
                'success': True,
                'message': 'Database refreshed successfully',
                'videos_count': len(videos),
                'videos': list(videos.keys())[:10]  # Show first 10 video IDs
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to refresh from Firebase'
            }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/videos')
def list_videos():
    """List all available videos"""
    try:
        videos = player_db.get_all_videos()
        video_list = []
        for video_id, video_data in videos.items():
            video_list.append({
                'id': video_id,
                'title': video_data.get('title', 'No title'),
                'url': f"/video-{video_id}",
                'views': video_data.get('views', 0),
                'created_at': video_data.get('created_at', 0),
                'expires_at': video_data.get('expires_at', 0),
                'expired': time.time() > video_data.get('expires_at', 0)
            })

        return jsonify({
            'success': True,
            'total_videos': len(video_list),
            'videos': video_list
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/protection')
def protection_dashboard():
    """Anti-scraping protection dashboard"""
    return render_template('protection_dashboard.html')

# Dashboard Authentication and Routes
DASHBOARD_PASSWORD = "1234567a"

def check_dashboard_auth():
    """Check if user is authenticated for dashboard access"""
    return session.get('dashboard_authenticated') == True

@app.route('/me/dash')
def dashboard():
    """Main dashboard - requires authentication"""
    if not check_dashboard_auth():
        return render_template('dashboard_login.html')

    return render_template('dashboard.html')

@app.route('/me/dash/login', methods=['POST'])
def dashboard_login():
    """Dashboard login endpoint"""
    password = request.form.get('password', '')

    if password == DASHBOARD_PASSWORD:
        session['dashboard_authenticated'] = True
        return redirect('/me/dash')
    else:
        return render_template('dashboard_login.html', error='Invalid password')

@app.route('/me/dash/logout')
def dashboard_logout():
    """Dashboard logout endpoint"""
    session.pop('dashboard_authenticated', None)
    return redirect('/me/dash')

@app.route('/api/dashboard/stats')
def dashboard_stats():
    """Get dashboard statistics - requires authentication"""
    if not check_dashboard_auth():
        abort(403)

    return jsonify(dashboard_db.get_dashboard_stats())

@app.route('/api/dashboard/clear_logs')
def dashboard_clear_logs():
    """Clear dashboard logs - requires authentication"""
    if not check_dashboard_auth():
        abort(403)

    dashboard_db.data['video_activity'] = []
    dashboard_db.data['active_sessions'] = {}
    dashboard_db.save_data()

    return jsonify({'success': True, 'message': 'Logs cleared successfully'})

@app.route('/api/my_ip')
def get_my_ip():
    """Get user's real public IP address"""
    try:
        client_ip = request.remote_addr
        real_ip = dashboard_db.get_real_ip_address(request)

        # Get geolocation for the real IP
        geo_data = dashboard_db.get_ip_geolocation(real_ip)

        return jsonify({
            'detected_ip': client_ip,
            'real_ip': real_ip,
            'geo_data': geo_data,
            'headers': {
                'X-Forwarded-For': request.headers.get('X-Forwarded-For'),
                'X-Real-IP': request.headers.get('X-Real-IP'),
                'CF-Connecting-IP': request.headers.get('CF-Connecting-IP'),
                'X-Client-IP': request.headers.get('X-Client-IP')
            }
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/analytics/page_load', methods=['POST'])
def analytics_page_load():
    """Handle page load analytics with comprehensive user information"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        video_id = data.get('video_id')
        user_info = data.get('user_info', {})

        client_ip = request.remote_addr
        real_ip = dashboard_db.get_real_ip_address(request)
        user_agent = request.headers.get('User-Agent', 'Unknown')

        print(f"📊 Page load analytics for {video_id} from {real_ip}")

        # Log comprehensive page load data
        dashboard_db.log_video_access(video_id, client_ip, user_agent, 'page_load_complete', user_info, request=request)

        # Extract and log interesting metrics
        if 'performance' in user_info and isinstance(user_info['performance'], dict):
            timing = user_info['performance'].get('timing', {})
            if isinstance(timing, dict) and 'navigationStart' in timing and 'loadEventEnd' in timing:
                load_time = timing['loadEventEnd'] - timing['navigationStart']
                print(f"⚡ Page load time: {load_time}ms")

        if 'storage' in user_info and isinstance(user_info['storage'], dict):
            quota = user_info['storage'].get('quota', {})
            if isinstance(quota, dict) and 'usagePercentage' in quota:
                print(f"💾 Storage usage: {quota['usagePercentage']}%")

        if 'canvas_fingerprint' in user_info:
            print(f"🎨 Canvas fingerprint: {user_info['canvas_fingerprint']}")

        if 'webgl' in user_info and isinstance(user_info['webgl'], dict):
            renderer = user_info['webgl'].get('renderer', 'Unknown')
            print(f"🎮 GPU: {renderer}")

        return jsonify({'success': True, 'message': 'Analytics data received'})

    except Exception as e:
        print(f"❌ Error processing page load analytics: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/log_devtools/<video_id>')
def log_devtools_detection(video_id):
    """Log DevTools detection from client-side"""
    monitor.log_devtools_detection(request.remote_addr, video_id)
    return jsonify({'status': 'logged'})

@app.route('/api/security_check')
def security_check():
    """Perform security check and return status"""
    user_agent = request.headers.get('User-Agent', '')

    # Check for automation
    automation_indicators = ['selenium', 'webdriver', 'phantom', 'headless']
    if any(indicator in user_agent.lower() for indicator in automation_indicators):
        monitor.log_automation_detection(request.remote_addr, user_agent)
        return jsonify({'status': 'blocked', 'reason': 'automation_detected'}), 403

    return jsonify({'status': 'allowed'})

@app.route('/api/process_drive_url', methods=['POST'])
def process_drive_url():
    """Test Google Drive URL processing"""
    data = request.get_json()
    if not data or 'url' not in data:
        return jsonify({'error': 'URL required'}), 400

    url = data['url']

    if not is_google_drive_url(url):
        return jsonify({'error': 'Not a Google Drive URL'}), 400

    file_id = extract_google_drive_file_id(url)
    if not file_id:
        return jsonify({'error': 'Could not extract file ID'}), 400

    stream_url = get_google_drive_stream_url(file_id)

    return jsonify({
        'original_url': url,
        'file_id': file_id,
        'stream_url': stream_url,
        'api_key_used': DRIVE_API_KEY[:10] + '...',  # Show partial key for verification
        'status': 'success'
    })

@app.route('/stream/secure/<token>')
def stream_secure_video(token):
    """Stream video with JWT token validation and masked URL"""
    payload = verify_jwt_token(token)

    if not payload:
        print(f"❌ Invalid or expired JWT token")
        abort(403)

    video_id = payload.get('video_id')
    video_url = payload.get('video_url')

    if not video_id or not video_url:
        print(f"❌ Invalid JWT payload")
        abort(403)

    # Get video data and update view count (session-based, async) with real IP
    video_data = player_db.get_video(video_id)
    if video_data:
        client_ip = request.remote_addr
        real_ip = dashboard_db.get_real_ip_address(request)
        user_agent = request.headers.get('User-Agent', '')
        session_id = f"{real_ip}_{user_agent}"
        threading.Thread(target=player_db.update_video_views_per_session, args=(video_id, session_id), daemon=True).start()
        print(f"📺 Streaming secure video {video_id} via JWT (Real IP: {real_ip}, Session: {session_id[:20]}...)")

    # Proxy the video content to completely hide real URLs
    try:
        print(f"🔒 Proxying secure video {video_id} (URL completely hidden)")

        # Make request to actual video URL with proper headers
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'video/webm,video/ogg,video/*;q=0.9,application/ogg;q=0.7,audio/*;q=0.6,*/*;q=0.5',
            'Accept-Encoding': 'identity'
        }

        # Handle range requests for video seeking
        if 'Range' in request.headers:
            headers['Range'] = request.headers['Range']

        # Make the proxied request
        response = requests.get(video_url, headers=headers, stream=True, timeout=30)

        # Create Flask response that proxies the content
        def generate():
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    yield chunk

        flask_response = Response(generate(),
                                status=response.status_code,
                                content_type=response.headers.get('Content-Type', 'video/mp4'))

        # Copy essential headers for video playback
        for header in ['Content-Length', 'Content-Range', 'Accept-Ranges', 'Content-Type']:
            if header in response.headers:
                flask_response.headers[header] = response.headers[header]

        # Add security headers
        flask_response.headers.update({
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'X-Robots-Tag': 'noindex, nofollow'
        })

        return flask_response

    except Exception as e:
        print(f"❌ Error proxying video: {e}")
        # Fallback to redirect if proxy fails
        return redirect(video_url)

@app.route('/stream/<video_id>')
def stream_video(video_id):
    """Optimized secure video streaming endpoint for multi-user handling"""
    token = request.args.get('token')
    if not token:
        abort(403)

    # Validate token (cached for performance)
    if not validate_video_token(token, video_id):
        abort(403)

    # Get video data with minimal database calls
    video_data = player_db.get_video(video_id)
    if not video_data:
        abort(404)

    # Check if video has expired
    if time.time() > video_data['expires_at']:
        abort(410)  # Gone

    # Async session-based view count update (non-blocking) with real IP
    client_ip = request.remote_addr
    real_ip = dashboard_db.get_real_ip_address(request)
    user_agent = request.headers.get('User-Agent', '')
    session_id = f"{real_ip}_{user_agent}"
    threading.Thread(target=player_db.update_video_views_per_session, args=(video_id, session_id), daemon=True).start()

    # Refresh Google Drive URL if needed (cached)
    if video_data.get('is_google_drive', False):
        # Use cached refresh to avoid repeated API calls
        url_hash = hashlib.md5(video_data.get('original_url', '').encode()).hexdigest()
        cached_url = cached_drive_file_id(url_hash)
        if cached_url:
            video_data['url'] = cached_url
        else:
            refresh_google_drive_url(video_data)
            # Cache the refreshed URL
            cached_drive_file_id.cache_clear()  # Clear to update cache
            cached_drive_file_id(url_hash)  # Re-cache
            # Async database update
            threading.Thread(target=player_db.save_video, args=(video_id, video_data), daemon=True).start()

    # Stream the video with optimized headers
    video_url = video_data['url']

    try:
        # For external URLs, redirect with proper headers
        if video_url.startswith('http'):
            response = requests.get(video_url, stream=True)

            def generate():
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        yield chunk

            return Response(
                generate(),
                content_type=response.headers.get('content-type', 'video/mp4'),
                headers={
                    'Accept-Ranges': 'bytes',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0',
                    'X-Content-Type-Options': 'nosniff'
                }
            )
        else:
            # For local files
            if os.path.exists(video_url):
                def generate():
                    with open(video_url, 'rb') as f:
                        while True:
                            chunk = f.read(8192)
                            if not chunk:
                                break
                            yield chunk

                return Response(
                    generate(),
                    content_type='video/mp4',
                    headers={
                        'Accept-Ranges': 'bytes',
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0',
                        'X-Content-Type-Options': 'nosniff'
                    }
                )
            else:
                abort(404)
    except Exception as e:
        print(f"Error streaming video {video_id}: {e}")
        abort(500)

if __name__ == '__main__':
    # Skip startup cleanup to prevent deleting valid videos
    print("🚀 Starting server without cleanup (Pure Firebase mode)")

    # Lightweight background sync (every 30 minutes to save CPU)
    def background_sync():
        while True:
            time.sleep(1800)  # 30 minutes
            try:
                # Only cleanup truly expired videos (not on startup)
                expired_count = player_db.cleanup_expired_videos()
                if expired_count > 0:
                    print(f"🧹 Background cleanup: removed {expired_count} expired videos")
            except Exception as e:
                print(f"⚠️ Background cleanup error: {e}")

    sync_thread = threading.Thread(target=background_sync, daemon=True)
    sync_thread.start()
    print("🔄 Background sync started (every 30 minutes)")

    app.run(debug=True, host='0.0.0.0', port=5000)
