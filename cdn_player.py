#!/usr/bin/env python3
"""
CDN Video Player - Allow users to submit video links and get short URLs
"""

from flask import Flask, render_template, request, jsonify, redirect, abort, Response
from flask_cors import CORS
import json
import os
import time
import hashlib
import secrets
import re
import hmac
import base64
import jwt
from datetime import datetime, timedelta
from urllib.parse import urlparse
import requests
from anti_scraping_monitor import monitor
from db import player_db, FIREBASE_CONFIG
import os
from dotenv import load_dotenv
import threading
import gc
from functools import lru_cache
import weakref

# Load environment variables
load_dotenv()

app = Flask(__name__)
CORS(app)

# Redirect configuration
REDIR_ENABLED = os.getenv('REDIR', 'False').lower() == 'true'
REDIR_LINK = os.getenv('REDIR_LINK', 'https://t.me/Hentai_Aria')

# Serve hot sexy favicon
@app.route('/favicon.ico')
def favicon():
    return app.send_static_file('favicon.ico')

# Anti-scraping configuration
SECURITY_HEADERS = {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; media-src *; img-src 'self' data:;",
    'Referrer-Policy': 'no-referrer',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
}

def add_security_headers(response):
    """Add comprehensive security headers"""
    for header, value in SECURITY_HEADERS.items():
        response.headers[header] = value
    return response

def is_bot_request():
    """Enhanced bot detection with smart browser recognition"""
    user_agent = request.headers.get('User-Agent', '').lower()

    # First, check if it's a legitimate browser
    legitimate_browsers = ['mozilla', 'chrome', 'firefox', 'safari', 'edge', 'opera']
    is_browser = any(browser in user_agent for browser in legitimate_browsers)

    # If it's a browser, be more lenient
    if is_browser:
        # Only block obvious automation tools even in browsers
        automation_patterns = ['selenium', 'webdriver', 'phantomjs', 'headless', 'automation']
        if any(pattern in user_agent for pattern in automation_patterns):
            return True
        # Allow browsers to access streaming without referer requirement
        return False

    # For non-browser user agents, be more strict
    bot_patterns = [
        'bot', 'crawler', 'spider', 'scraper', 'wget', 'curl',
        'python', 'requests', 'urllib', 'scrapy', 'postman',
        'insomnia', 'httpie', 'axios', 'fetch', 'node', 'go-http',
        'java', 'okhttp', 'apache', 'libwww', 'winhttp', 'cfnetwork',
        'download', 'monitor', 'check', 'scan', 'probe', 'validator'
    ]

    # Check user agent for bot patterns
    if any(pattern in user_agent for pattern in bot_patterns):
        # Allow legitimate search engine bots
        if not any(allowed in user_agent for allowed in ['googlebot', 'bingbot', 'slurp']):
            return True

    # Check for empty or very short user agents
    if not user_agent or len(user_agent) < 10:
        return True

    # For streaming endpoints from non-browsers, require proper referer
    if request.endpoint == 'stream_video':
        referer = request.headers.get('Referer', '')
        if not referer or request.host not in referer:
            return True

    return False

@app.before_request
def before_request():
    """Lightweight request preprocessing - optimized for low CPU/RAM"""
    # Skip all heavy processing for static files and API stats
    if request.endpoint in ['static'] or request.path.endswith(('.css', '.js', '.png', '.jpg')) or request.path == '/api/stats':
        return None

    # Minimal bot blocking - only block obvious curl/wget
    user_agent = request.headers.get('User-Agent', '').lower()
    if any(bot in user_agent for bot in ['curl', 'wget', 'python-requests']):
        print(f"BLOCKED bot: {user_agent[:50]}")
        abort(403)

    # Minimal session tracking (no rate limiting to save CPU)
    session_id = user_manager.track_user(request)

@app.after_request
def after_request(response):
    """Lightweight response post-processing - optimized for low CPU/RAM"""
    # Minimal security headers only
    response.headers.update({
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
    })

    # Cache static files only
    if request.path.endswith(('.css', '.js', '.png', '.jpg')):
        response.cache_control.max_age = 3600

    return response

# Template filters
@app.template_filter('timestamp_to_date')
def timestamp_to_date(timestamp):
    """Convert timestamp to readable date"""
    try:
        return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M')
    except:
        return 'Unknown'

# Configuration
MAX_VIDEO_SIZE_MB = 500
ALLOWED_DOMAINS = ['youtube.com', 'youtu.be', 'vimeo.com', 'dailymotion.com', 'twitch.tv', 'imgur.com', 'giphy.com', 'drive.google.com', 'docs.google.com']
DEFAULT_EXPIRE_DAYS = 30
SECRET_KEY = secrets.token_hex(32)  # For token generation
JWT_SECRET = os.getenv('JWT_SECRET', secrets.token_hex(32))  # JWT secret for masked URLs
TOKEN_EXPIRY_MINUTES = 60  # Token expires in 1 hour

# Google Drive configuration
DRIVE_API_KEY = os.getenv('DRIVE_API', 'AIzaSyAd2xbht3di_oA1nBhcme0LxiKAtPYH-vs')
DRIVE_STREAM_URL_TEMPLATE = os.getenv('DRIVE_STREAM_URL', 'https://www.googleapis.com/drive/v3/files/{fileid}?alt=media&key={APIKey}')

# Multi-user optimization settings
MAX_CONCURRENT_USERS = 100
SESSION_TIMEOUT = 1800  # 30 minutes
MEMORY_CLEANUP_INTERVAL = 300  # 5 minutes
MAX_CACHE_SIZE = 50  # Maximum cached items per type

class MultiUserManager:
    """Lightweight multi-user session manager with memory optimization"""

    def __init__(self):
        self.active_sessions = weakref.WeakValueDictionary()
        self.session_data = {}
        self.last_cleanup = time.time()
        self.lock = threading.RLock()

    def get_session_id(self, request):
        """Generate or retrieve session ID for user"""
        user_ip = request.remote_addr
        user_agent = request.headers.get('User-Agent', '')[:50]  # Limit length
        session_key = hashlib.md5(f"{user_ip}:{user_agent}".encode()).hexdigest()[:16]
        return session_key

    def track_user(self, request):
        """Track user session with minimal memory footprint"""
        session_id = self.get_session_id(request)
        current_time = time.time()

        with self.lock:
            # Cleanup old sessions periodically
            if current_time - self.last_cleanup > MEMORY_CLEANUP_INTERVAL:
                self.cleanup_sessions()
                self.last_cleanup = current_time

            # Update session
            self.session_data[session_id] = {
                'last_seen': current_time,
                'ip': request.remote_addr,
                'requests': self.session_data.get(session_id, {}).get('requests', 0) + 1
            }

            return session_id

    def cleanup_sessions(self):
        """Remove expired sessions and force garbage collection"""
        current_time = time.time()
        expired_sessions = []

        for session_id, data in self.session_data.items():
            if current_time - data['last_seen'] > SESSION_TIMEOUT:
                expired_sessions.append(session_id)

        for session_id in expired_sessions:
            del self.session_data[session_id]

        # Force garbage collection
        gc.collect()

        if expired_sessions:
            print(f"🧹 Cleaned {len(expired_sessions)} expired sessions")

    def get_active_users(self):
        """Get count of active users"""
        current_time = time.time()
        active_count = sum(1 for data in self.session_data.values()
                          if current_time - data['last_seen'] < SESSION_TIMEOUT)
        return active_count

    def is_rate_limited(self, session_id, max_requests_per_minute=60):
        """Simple rate limiting per session"""
        if session_id not in self.session_data:
            return False

        session = self.session_data[session_id]
        current_time = time.time()

        # Reset counter every minute
        if current_time - session.get('rate_limit_reset', 0) > 60:
            session['rate_limit_count'] = 0
            session['rate_limit_reset'] = current_time

        session['rate_limit_count'] = session.get('rate_limit_count', 0) + 1
        return session['rate_limit_count'] > max_requests_per_minute

# Global multi-user manager
user_manager = MultiUserManager()

# Memory-efficient caching
@lru_cache(maxsize=MAX_CACHE_SIZE)
def cached_video_validation(url_hash):
    """Cache video URL validation results"""
    return True  # Placeholder for validation logic

@lru_cache(maxsize=MAX_CACHE_SIZE)
def cached_drive_file_id(url_hash):
    """Cache Google Drive file ID extraction"""
    # This will be populated by the actual extraction function
    return None

def clear_caches():
    """Clear all LRU caches to free memory"""
    cached_video_validation.cache_clear()
    cached_drive_file_id.cache_clear()
    gc.collect()

# Memory optimization middleware
def optimize_memory():
    """Periodic memory optimization"""
    def memory_worker():
        while True:
            time.sleep(MEMORY_CLEANUP_INTERVAL)
            user_manager.cleanup_sessions()
            clear_caches()
            gc.collect()

    memory_thread = threading.Thread(target=memory_worker, daemon=True)
    memory_thread.start()
    print("🧠 Memory optimization started")

# Start memory optimization
optimize_memory()

def load_cdn_data():
    """Load CDN video data from Firebase with local cache"""
    return player_db.get_all_videos()

def save_cdn_data(data):
    """Save CDN video data to Firebase (deprecated - use individual video saves)"""
    # This function is kept for compatibility but individual saves are preferred
    for video_id, video_data in data.items():
        player_db.save_video(video_id, video_data)

def generate_short_id():
    """Generate a short unique ID for video"""
    return secrets.token_urlsafe(6).replace('-', '').replace('_', '')[:6].lower()

def generate_video_token(video_id, expiry_minutes=TOKEN_EXPIRY_MINUTES):
    """Generate a secure token for video access"""
    timestamp = int(time.time() + (expiry_minutes * 60))
    message = f"{video_id}:{timestamp}"
    signature = hmac.new(
        SECRET_KEY.encode(),
        message.encode(),
        hashlib.sha256
    ).hexdigest()
    token = base64.b64encode(f"{message}:{signature}".encode()).decode()
    return token

def generate_jwt_masked_url(video_url, video_id, expiry_hours=1):
    """Generate JWT-based masked URL with 1-hour expiry"""
    try:
        payload = {
            'video_id': video_id,
            'video_url': video_url,
            'iat': datetime.now(datetime.UTC),
            'exp': datetime.now(datetime.UTC) + timedelta(hours=expiry_hours),
            'iss': 'cdn-player',
            'aud': 'video-stream'
        }

        token = jwt.encode(payload, JWT_SECRET, algorithm='HS256')
        masked_url = f"/stream/secure/{token}"

        print(f"🔒 Generated JWT masked URL for {video_id}: {masked_url[:50]}...")
        return masked_url

    except Exception as e:
        print(f"❌ Error generating JWT masked URL: {e}")
        return video_url  # Fallback to original URL

def verify_jwt_token(token):
    """Lightweight JWT verification - optimized for low CPU"""
    try:
        # Simple decode with minimal verification for performance
        payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'], options={"verify_exp": False, "verify_aud": False})
        return payload
    except Exception:
        # Fallback: allow any valid JWT structure
        try:
            payload = jwt.decode(token, options={"verify_signature": False})
            return payload
        except Exception:
            return None

def validate_video_token(token, video_id):
    """Validate a video access token"""
    try:
        decoded = base64.b64decode(token.encode()).decode()
        parts = decoded.split(':')
        if len(parts) != 3:
            return False

        token_video_id, timestamp_str, signature = parts
        timestamp = int(timestamp_str)

        # Check if token is for the correct video
        if token_video_id != video_id:
            return False

        # Check if token has expired
        if time.time() > timestamp:
            return False

        # Verify signature
        message = f"{token_video_id}:{timestamp_str}"
        expected_signature = hmac.new(
            SECRET_KEY.encode(),
            message.encode(),
            hashlib.sha256
        ).hexdigest()

        return hmac.compare_digest(signature, expected_signature)
    except:
        return False

def extract_google_drive_file_id(url):
    """Extract file ID from Google Drive URL"""
    try:
        # Handle different Google Drive URL formats
        patterns = [
            r'/file/d/([a-zA-Z0-9-_]+)',  # https://drive.google.com/file/d/FILE_ID/view
            r'id=([a-zA-Z0-9-_]+)',       # https://drive.google.com/open?id=FILE_ID
            r'/d/([a-zA-Z0-9-_]+)',       # https://docs.google.com/document/d/FILE_ID
        ]

        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)

        return None
    except:
        return None

def get_google_drive_stream_url(file_id):
    """Convert Google Drive file ID to direct stream URL"""
    return DRIVE_STREAM_URL_TEMPLATE.format(fileid=file_id, APIKey=DRIVE_API_KEY)

def is_google_drive_url(url):
    """Check if URL is a Google Drive URL"""
    return any(domain in url.lower() for domain in ['drive.google.com', 'docs.google.com'])

def detect_video_platform(url):
    """Detect video platform and return platform info with direct streaming URL"""
    url_lower = url.lower()

    # Google Drive
    if is_google_drive_url(url):
        file_id = extract_google_drive_file_id(url)
        if file_id:
            return {
                'platform': 'google_drive',
                'file_id': file_id,
                'direct_url': get_google_drive_stream_url(file_id),
                'needs_processing': True,
                'supports_seeking': True
            }

    # Catbox.moe - Direct file hosting
    elif 'catbox.moe' in url_lower:
        if 'files.catbox.moe' in url_lower:
            return {
                'platform': 'catbox',
                'direct_url': url,
                'needs_processing': False,
                'supports_seeking': True
            }
        elif 'catbox.moe/v/' in url_lower:
            # Convert catbox.moe/v/xxx to files.catbox.moe/xxx
            file_id = url_lower.split('/v/')[-1].split('?')[0]
            return {
                'platform': 'catbox',
                'direct_url': f"https://files.catbox.moe/{file_id}",
                'needs_processing': False,
                'supports_seeking': True
            }

    # Imgur - Image and video hosting
    elif 'imgur.com' in url_lower:
        if 'i.imgur.com' in url_lower:
            return {
                'platform': 'imgur',
                'direct_url': url,
                'needs_processing': False,
                'supports_seeking': True
            }
        elif 'imgur.com/' in url_lower:
            # Convert imgur.com/xxx to i.imgur.com/xxx.mp4
            file_id = url_lower.split('imgur.com/')[-1].split('?')[0].split('.')[0]
            # Try multiple formats
            for ext in ['.mp4', '.webm', '.gif']:
                test_url = f"https://i.imgur.com/{file_id}{ext}"
                return {
                    'platform': 'imgur',
                    'direct_url': test_url,
                    'needs_processing': False,
                    'supports_seeking': True
                }

    # Streamable
    elif 'streamable.com' in url_lower:
        video_id = url_lower.split('streamable.com/')[-1].split('?')[0]
        return {
            'platform': 'streamable',
            'direct_url': f"https://cdn-cf-east.streamable.com/video/mp4/{video_id}.mp4",
            'needs_processing': False,
            'supports_seeking': True
        }

    # Gfycat
    elif 'gfycat.com' in url_lower:
        video_id = url_lower.split('gfycat.com/')[-1].split('?')[0]
        return {
            'platform': 'gfycat',
            'direct_url': f"https://giant.gfycat.com/{video_id}.mp4",
            'needs_processing': False,
            'supports_seeking': True
        }

    # Reddit video
    elif 'v.redd.it' in url_lower:
        base_url = url.rstrip('/')
        return {
            'platform': 'reddit',
            'direct_url': f"{base_url}/DASH_720.mp4" if not url.endswith('.mp4') else url,
            'needs_processing': False,
            'supports_seeking': True
        }

    # Discord CDN
    elif 'cdn.discordapp.com' in url_lower or 'media.discordapp.net' in url_lower:
        return {
            'platform': 'discord',
            'direct_url': url,
            'needs_processing': False,
            'supports_seeking': True
        }

    # Direct video files
    elif any(ext in url_lower for ext in ['.mp4', '.webm', '.avi', '.mov', '.mkv', '.m4v', '.flv', '.wmv']):
        return {
            'platform': 'direct',
            'direct_url': url,
            'needs_processing': False,
            'supports_seeking': True
        }

    # Default fallback - try as direct URL
    return {
        'platform': 'unknown',
        'direct_url': url,
        'needs_processing': False,
        'supports_seeking': False
    }

def process_google_drive_url(url):
    """Process Google Drive URL and return streamable URL"""
    file_id = extract_google_drive_file_id(url)
    if file_id:
        stream_url = get_google_drive_stream_url(file_id)
        print(f"🔗 Google Drive URL processed: {file_id} -> {stream_url}")
        return stream_url
    return None

def is_valid_video_url(url):
    """Check if URL is a valid video URL using enhanced platform detection"""
    try:
        parsed = urlparse(url)
        if not parsed.scheme or not parsed.netloc:
            return False

        # Use platform detection for validation
        platform_info = detect_video_platform(url)

        # Supported platforms
        supported_platforms = [
            'google_drive', 'catbox', 'imgur', 'streamable',
            'gfycat', 'reddit', 'discord', 'direct'
        ]

        if platform_info['platform'] in supported_platforms:
            print(f"✅ Valid {platform_info['platform']} URL detected")
            return True

        # Check file extension for direct video files
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.webm', '.gif', '.m4v', '.flv', '.wmv']
        if any(url.lower().endswith(ext) for ext in video_extensions):
            print(f"✅ Valid direct video file detected")
            return True

        # Check allowed domains (fallback)
        domain = parsed.netloc.lower()
        allowed_domains = [
            'drive.google.com', 'docs.google.com',
            'catbox.moe', 'files.catbox.moe',
            'imgur.com', 'i.imgur.com',
            'streamable.com', 'cdn-cf-east.streamable.com',
            'gfycat.com', 'giant.gfycat.com',
            'v.redd.it', 'reddit.com',
            'cdn.discordapp.com', 'media.discordapp.net'
        ]

        if any(allowed in domain for allowed in allowed_domains):
            print(f"✅ Valid domain detected: {domain}")
            return True

        # Unknown platform - allow but warn
        print(f"⚠️ Unknown platform, allowing: {domain}")
        return True  # Be permissive for unknown platforms

    except Exception as e:
        print(f"❌ URL validation error: {e}")
        return False

def check_video_accessibility(url):
    """Check if video URL is accessible"""
    try:
        response = requests.head(url, timeout=10, allow_redirects=True)
        return response.status_code == 200
    except:
        return False

def refresh_google_drive_url(video_data):
    """Refresh Google Drive stream URL if needed"""
    if video_data.get('is_google_drive', False):
        original_url = video_data.get('original_url', '')
        if original_url:
            new_stream_url = process_google_drive_url(original_url)
            if new_stream_url:
                video_data['url'] = new_stream_url
                print(f"🔄 Google Drive URL refreshed for video")
                return True
    return False

def clean_expired_videos():
    """Remove expired videos from Firebase and cache"""
    return player_db.cleanup_expired_videos()

@app.route('/')
def homepage():
    """CDN Player Homepage"""
    return render_template('cdn_homepage.html')

@app.route('/api/submit_video', methods=['POST'])
def submit_video():
    """Submit a video URL and get a short link"""
    try:
        data = request.get_json()
        video_url = data.get('url', '').strip()
        custom_expire = data.get('expire_days')
        title = data.get('title', '').strip()
        
        if not video_url:
            return jsonify({'error': 'Video URL is required'}), 400
        
        # Validate URL
        if not is_valid_video_url(video_url):
            return jsonify({'error': 'Invalid video URL or unsupported domain'}), 400

        # Detect and process video platform
        original_url = video_url
        platform_info = detect_video_platform(video_url)

        if platform_info['platform'] == 'google_drive':
            if platform_info.get('direct_url'):
                video_url = platform_info['direct_url']
                print(f"📁 Google Drive URL converted: {original_url} -> {video_url}")
            else:
                return jsonify({'error': 'Could not process Google Drive URL'}), 400
        elif platform_info['platform'] in ['catbox', 'imgur', 'streamable', 'gfycat', 'reddit', 'discord', 'direct']:
            video_url = platform_info['direct_url']
            print(f"🎬 {platform_info['platform'].title()} URL processed: {original_url} -> {video_url}")
        elif platform_info['platform'] == 'unknown':
            print(f"⚠️ Unknown platform, trying direct access: {video_url}")
        else:
            video_url = platform_info['direct_url']

        # Check accessibility (optional, can be slow)
        if not check_video_accessibility(video_url):
            return jsonify({'error': 'Video URL is not accessible'}), 400
        
        # Generate short ID
        video_id = generate_short_id()

        # Ensure unique ID
        cdn_data = load_cdn_data()
        while video_id in cdn_data:
            video_id = generate_short_id()

        # Calculate expiry
        expire_days = custom_expire if custom_expire and 1 <= custom_expire <= 365 else DEFAULT_EXPIRE_DAYS
        expires_at = time.time() + (expire_days * 24 * 60 * 60)

        # Store video data with platform information
        video_data = {
            'url': video_url,
            'original_url': original_url,  # Store original URL for reference
            'title': title or f'Video {video_id}',
            'created_at': time.time(),
            'expires_at': expires_at,
            'expire_days': expire_days,
            'views': 0,
            'last_viewed': None,
            'creator_ip': request.remote_addr,
            'platform': platform_info['platform'],
            'supports_seeking': platform_info.get('supports_seeking', False),
            'is_google_drive': platform_info['platform'] == 'google_drive'
        }

        # Save to Firebase immediately
        save_success = player_db.save_video(video_id, video_data)
        if save_success:
            print(f"✅ Video {video_id} saved to Firebase successfully")

            # Verify it was saved by retrieving it
            verification = player_db.get_video(video_id)
            if verification:
                print(f"✅ Video {video_id} verified in Firebase")
            else:
                print(f"❌ Video {video_id} save verification failed!")
        else:
            print(f"❌ Failed to save video {video_id} to Firebase")
            return jsonify({'error': 'Failed to save video to database'}), 500

        # Generate short URL
        short_url = f"{request.host_url}video-{video_id}"

        return jsonify({
            'success': True,
            'video_id': video_id,
            'short_url': short_url,
            'expires_at': datetime.fromtimestamp(expires_at).isoformat(),
            'expire_days': expire_days,
            'firebase_saved': save_success
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/video-<video_id>')
def play_video(video_id):
    """Play video from short URL with anti-scraping protection"""
    print(f"🔍 Attempting to load video: {video_id}")

    # Get video data directly from Firebase
    video_data = player_db.get_video(video_id)

    if not video_data:
        print(f"❌ Video {video_id} not found in Firebase")
        # Let's also check what videos exist
        all_videos = player_db.get_all_videos()
        print(f"📋 Available videos: {list(all_videos.keys())}")
        abort(404)

    print(f"✅ Video {video_id} found: {video_data.get('title', 'No title')}")

    # Check if expired
    current_time = time.time()
    expires_at = video_data.get('expires_at', 0)
    if current_time > expires_at:
        print(f"⏰ Video {video_id} expired (current: {current_time}, expires: {expires_at})")
        # Remove expired video
        player_db.delete_video(video_id)
        abort(410)  # Gone

    print(f"🔒 Loading video player for {video_id} (URL will be fetched dynamically)")

    return render_template('cdn_player.html',
                         video_data=video_data,
                         video_id=video_id,
                         redir_enabled=REDIR_ENABLED,
                         redir_link=REDIR_LINK)

@app.route('/api/video_info/<video_id>')
def get_video_info(video_id):
    """Get video information"""
    video_data = player_db.get_video(video_id)

    if not video_data:
        return jsonify({'error': 'Video not found'}), 404

    # Check if expired
    if time.time() > video_data.get('expires_at', 0):
        return jsonify({'error': 'Video expired'}), 410

    # Return safe info (without creator IP)
    safe_data = {
        'title': video_data['title'],
        'created_at': video_data['created_at'],
        'expires_at': video_data['expires_at'],
        'views': video_data['views'],
        'last_viewed': video_data.get('last_viewed')
    }

    return jsonify(safe_data)

@app.route('/api/cleanup')
def cleanup_expired():
    """Manual cleanup of expired videos"""
    cleaned = clean_expired_videos()
    return jsonify({'cleaned': cleaned, 'message': f'Removed {cleaned} expired videos'})

@app.route('/api/get_secure_url/<video_id>')
def get_secure_url(video_id):
    """Get secure video URL dynamically"""
    try:
        # Basic bot protection
        user_agent = request.headers.get('User-Agent', '')
        if not user_agent or 'curl' in user_agent.lower() or 'wget' in user_agent.lower():
            abort(403)

        cdn_data = load_cdn_data()
        if video_id not in cdn_data:
            return jsonify({'error': 'Video not found'}), 404

        video_data = cdn_data[video_id]
        current_time = time.time()

        if current_time > video_data.get('expires_at', 0):
            return jsonify({'error': 'Video expired'}), 410

        # Generate secure token
        secure_url = generate_jwt_masked_url(video_data['url'], video_id, expiry_hours=1)

        return jsonify({
            'secure_url': secure_url,
            'expires_in': 3600  # 1 hour
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/count_view/<video_id>', methods=['POST'])
def count_view(video_id):
    """Count view with session-based tracking (1 view per session per video)"""
    try:
        # Basic bot protection
        user_agent = request.headers.get('User-Agent', '')
        if not user_agent or 'curl' in user_agent.lower() or 'wget' in user_agent.lower():
            abort(403)

        # Get video data from Firebase
        video_data = player_db.get_video(video_id)
        if not video_data:
            return jsonify({'error': 'Video not found'}), 404

        # Check if video has expired
        current_time = time.time()
        if current_time > video_data.get('expires_at', 0):
            return jsonify({'error': 'Video expired'}), 410

        # Session-based view counting
        client_ip = request.remote_addr
        session_id = request.headers.get('X-Session-ID', f"{client_ip}_{user_agent}")

        # Use session-based view counting
        threading.Thread(
            target=player_db.update_video_views_per_session,
            args=(video_id, session_id),
            daemon=True
        ).start()

        print(f"📊 Session-based view count triggered for video {video_id} from session: {session_id[:20]}...")

        return jsonify({'status': 'success', 'message': 'View counted'})

    except Exception as e:
        print(f"❌ Error counting view for {video_id}: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats')
def get_stats():
    """Get CDN player statistics with Firebase integration"""
    # Get stats from Firebase
    stats = player_db.get_stats()
    cdn_data = load_cdn_data()
    current_time = time.time()

    total_videos = len(cdn_data)
    active_videos = sum(1 for v in cdn_data.values() if current_time <= v.get('expires_at', 0))
    expired_videos = total_videos - active_videos
    total_views = sum(v.get('views', 0) for v in cdn_data.values())

    # Calculate recent videos
    recent_videos = []
    sorted_videos = sorted(cdn_data.items(), key=lambda x: x[1].get('created_at', 0), reverse=True)
    for video_id, video_data in sorted_videos[:5]:
        recent_videos.append({
            'id': video_id,
            'title': video_data['title'],
            'views': video_data['views'],
            'created_at': video_data['created_at']
        })

    return jsonify({
        'total_videos': total_videos,
        'active_videos': active_videos,
        'expired_videos': expired_videos,
        'total_views': total_views,
        'recent_videos': recent_videos,
        'firebase_connected': player_db.connected,
        'last_sync': stats.get('last_updated', time.time())
    })

@app.route('/api/protection_report')
def get_protection_report():
    """Get anti-scraping protection report"""
    return jsonify(monitor.get_protection_report())

@app.route('/api/database_status')
def get_database_status():
    """Get database connection and status information"""
    try:
        # Get detailed status from database
        status = player_db.get_connection_status()

        # Test database operations
        test_data = player_db.get_all_videos()
        can_read = len(test_data) >= 0

        return jsonify({
            'firebase_connected': status['firebase_connected'],
            'firebase_videos_count': status['firebase_videos'],
            'mode': status['mode'],
            'cache_disabled': status['cache_disabled'],
            'can_read_data': can_read,
            'total_videos_accessible': len(test_data),
            'stats': status['stats'],
            'firebase_config': {
                'project_id': FIREBASE_CONFIG.get('projectId', 'unknown'),
                'database_url': FIREBASE_CONFIG.get('databaseURL', 'unknown')
            }
        })
    except Exception as e:
        return jsonify({
            'error': str(e),
            'firebase_connected': False,
            'firebase_videos_count': 0
        }), 500

@app.route('/api/database_refresh', methods=['POST'])
def refresh_database():
    """Force refresh database from Firebase"""
    try:
        success = player_db.force_refresh_from_firebase()
        if success:
            videos = player_db.get_all_videos()
            return jsonify({
                'success': True,
                'message': 'Database refreshed successfully',
                'videos_count': len(videos),
                'videos': list(videos.keys())[:10]  # Show first 10 video IDs
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to refresh from Firebase'
            }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/videos')
def list_videos():
    """List all available videos"""
    try:
        videos = player_db.get_all_videos()
        video_list = []
        for video_id, video_data in videos.items():
            video_list.append({
                'id': video_id,
                'title': video_data.get('title', 'No title'),
                'url': f"/video-{video_id}",
                'views': video_data.get('views', 0),
                'created_at': video_data.get('created_at', 0),
                'expires_at': video_data.get('expires_at', 0),
                'expired': time.time() > video_data.get('expires_at', 0)
            })

        return jsonify({
            'success': True,
            'total_videos': len(video_list),
            'videos': video_list
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/protection')
def protection_dashboard():
    """Anti-scraping protection dashboard"""
    return render_template('protection_dashboard.html')

@app.route('/api/log_devtools/<video_id>')
def log_devtools_detection(video_id):
    """Log DevTools detection from client-side"""
    monitor.log_devtools_detection(request.remote_addr, video_id)
    return jsonify({'status': 'logged'})

@app.route('/api/security_check')
def security_check():
    """Perform security check and return status"""
    user_agent = request.headers.get('User-Agent', '')

    # Check for automation
    automation_indicators = ['selenium', 'webdriver', 'phantom', 'headless']
    if any(indicator in user_agent.lower() for indicator in automation_indicators):
        monitor.log_automation_detection(request.remote_addr, user_agent)
        return jsonify({'status': 'blocked', 'reason': 'automation_detected'}), 403

    return jsonify({'status': 'allowed'})

@app.route('/api/process_drive_url', methods=['POST'])
def process_drive_url():
    """Test Google Drive URL processing"""
    data = request.get_json()
    if not data or 'url' not in data:
        return jsonify({'error': 'URL required'}), 400

    url = data['url']

    if not is_google_drive_url(url):
        return jsonify({'error': 'Not a Google Drive URL'}), 400

    file_id = extract_google_drive_file_id(url)
    if not file_id:
        return jsonify({'error': 'Could not extract file ID'}), 400

    stream_url = get_google_drive_stream_url(file_id)

    return jsonify({
        'original_url': url,
        'file_id': file_id,
        'stream_url': stream_url,
        'api_key_used': DRIVE_API_KEY[:10] + '...',  # Show partial key for verification
        'status': 'success'
    })

@app.route('/stream/secure/<token>')
def stream_secure_video(token):
    """Stream video with JWT token validation and masked URL"""
    payload = verify_jwt_token(token)

    if not payload:
        print(f"❌ Invalid or expired JWT token")
        abort(403)

    video_id = payload.get('video_id')
    video_url = payload.get('video_url')

    if not video_id or not video_url:
        print(f"❌ Invalid JWT payload")
        abort(403)

    # Get video data and update view count (session-based, async)
    video_data = player_db.get_video(video_id)
    if video_data:
        client_ip = request.remote_addr
        user_agent = request.headers.get('User-Agent', '')
        session_id = f"{client_ip}_{user_agent}"
        threading.Thread(target=player_db.update_video_views_per_session, args=(video_id, session_id), daemon=True).start()
        print(f"📺 Streaming secure video {video_id} via JWT (Session: {session_id[:20]}...)")

    # Proxy the video content to completely hide real URLs
    try:
        print(f"🔒 Proxying secure video {video_id} (URL completely hidden)")

        # Make request to actual video URL with proper headers
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'video/webm,video/ogg,video/*;q=0.9,application/ogg;q=0.7,audio/*;q=0.6,*/*;q=0.5',
            'Accept-Encoding': 'identity'
        }

        # Handle range requests for video seeking
        if 'Range' in request.headers:
            headers['Range'] = request.headers['Range']

        # Make the proxied request
        response = requests.get(video_url, headers=headers, stream=True, timeout=30)

        # Create Flask response that proxies the content
        def generate():
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    yield chunk

        flask_response = Response(generate(),
                                status=response.status_code,
                                content_type=response.headers.get('Content-Type', 'video/mp4'))

        # Copy essential headers for video playback
        for header in ['Content-Length', 'Content-Range', 'Accept-Ranges', 'Content-Type']:
            if header in response.headers:
                flask_response.headers[header] = response.headers[header]

        # Add security headers
        flask_response.headers.update({
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'X-Robots-Tag': 'noindex, nofollow'
        })

        return flask_response

    except Exception as e:
        print(f"❌ Error proxying video: {e}")
        # Fallback to redirect if proxy fails
        return redirect(video_url)

@app.route('/stream/<video_id>')
def stream_video(video_id):
    """Optimized secure video streaming endpoint for multi-user handling"""
    token = request.args.get('token')
    if not token:
        abort(403)

    # Validate token (cached for performance)
    if not validate_video_token(token, video_id):
        abort(403)

    # Get video data with minimal database calls
    video_data = player_db.get_video(video_id)
    if not video_data:
        abort(404)

    # Check if video has expired
    if time.time() > video_data['expires_at']:
        abort(410)  # Gone

    # Async session-based view count update (non-blocking)
    client_ip = request.remote_addr
    user_agent = request.headers.get('User-Agent', '')
    session_id = f"{client_ip}_{user_agent}"
    threading.Thread(target=player_db.update_video_views_per_session, args=(video_id, session_id), daemon=True).start()

    # Refresh Google Drive URL if needed (cached)
    if video_data.get('is_google_drive', False):
        # Use cached refresh to avoid repeated API calls
        url_hash = hashlib.md5(video_data.get('original_url', '').encode()).hexdigest()
        cached_url = cached_drive_file_id(url_hash)
        if cached_url:
            video_data['url'] = cached_url
        else:
            refresh_google_drive_url(video_data)
            # Cache the refreshed URL
            cached_drive_file_id.cache_clear()  # Clear to update cache
            cached_drive_file_id(url_hash)  # Re-cache
            # Async database update
            threading.Thread(target=player_db.save_video, args=(video_id, video_data), daemon=True).start()

    # Stream the video with optimized headers
    video_url = video_data['url']

    try:
        # For external URLs, redirect with proper headers
        if video_url.startswith('http'):
            response = requests.get(video_url, stream=True)

            def generate():
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        yield chunk

            return Response(
                generate(),
                content_type=response.headers.get('content-type', 'video/mp4'),
                headers={
                    'Accept-Ranges': 'bytes',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0',
                    'X-Content-Type-Options': 'nosniff'
                }
            )
        else:
            # For local files
            if os.path.exists(video_url):
                def generate():
                    with open(video_url, 'rb') as f:
                        while True:
                            chunk = f.read(8192)
                            if not chunk:
                                break
                            yield chunk

                return Response(
                    generate(),
                    content_type='video/mp4',
                    headers={
                        'Accept-Ranges': 'bytes',
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0',
                        'X-Content-Type-Options': 'nosniff'
                    }
                )
            else:
                abort(404)
    except Exception as e:
        print(f"Error streaming video {video_id}: {e}")
        abort(500)

if __name__ == '__main__':
    # Skip startup cleanup to prevent deleting valid videos
    print("🚀 Starting server without cleanup (Pure Firebase mode)")

    # Lightweight background sync (every 30 minutes to save CPU)
    def background_sync():
        while True:
            time.sleep(1800)  # 30 minutes
            try:
                # Only cleanup truly expired videos (not on startup)
                expired_count = player_db.cleanup_expired_videos()
                if expired_count > 0:
                    print(f"🧹 Background cleanup: removed {expired_count} expired videos")
            except Exception as e:
                print(f"⚠️ Background cleanup error: {e}")

    sync_thread = threading.Thread(target=background_sync, daemon=True)
    sync_thread.start()
    print("🔄 Background sync started (every 30 minutes)")

    app.run(debug=True, host='0.0.0.0', port=5000)
