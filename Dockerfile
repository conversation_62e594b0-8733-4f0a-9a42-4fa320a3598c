# Multi-user CDN Video Player - Cross-platform, Python 3.8+
FROM python:3-slim

# Set environment variables for optimization
ENV PYTHONOPTIMIZE=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    FLASK_ENV=production \
    MALLOC_ARENA_MAX=2 \
    MALLOC_MMAP_THRESHOLD_=131072 \
    MALLOC_TRIM_THRESHOLD_=131072 \
    MALLOC_TOP_PAD_=131072 \
    MALLOC_MMAP_MAX_=65536

# Install system dependencies (cross-platform compatible)
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user for security
RUN useradd --create-home --shell /bin/bash --user-group cdnuser

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir gunicorn[eventlet]

# Copy application files
COPY --chown=cdnuser:cdnuser . .

# Create necessary directories
RUN mkdir -p /app/logs /app/cache && \
    chown -R cdnuser:cdnuser /app

# Switch to non-root user
USER cdnuser

# Health check (cross-platform compatible)
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import urllib.request; urllib.request.urlopen('http://localhost:5000/api/stats', timeout=5)"

# Expose port
EXPOSE 5000

# Start command
CMD ["gunicorn", "--config", "gunicorn_config.py", "cdn_player:app"]
