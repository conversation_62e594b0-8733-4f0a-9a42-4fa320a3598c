#!/bin/bash
# Build script for Render deployment
# Handles Python 3.13+ compatibility issues

set -e

echo "🚀 Starting build process..."

# Check Python version
PYTHON_VERSION=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
echo "📍 Python version: $PYTHON_VERSION"

# Install system dependencies if needed
if command -v apt-get &> /dev/null; then
    echo "📦 Installing system dependencies..."
    apt-get update -qq
    apt-get install -y -qq build-essential
fi

# Upgrade pip
echo "⬆️ Upgrading pip..."
python3 -m pip install --upgrade pip

# Determine which requirements to use
if python3 -c "import sys; exit(0 if sys.version_info >= (3, 13) else 1)" 2>/dev/null; then
    echo "🐍 Using Python 3.13+ compatible requirements..."
    REQUIREMENTS_FILE="requirements-py313.txt"
    WORKER_CLASS="eventlet"
else
    echo "🐍 Using standard requirements..."
    REQUIREMENTS_FILE="requirements.txt"
    WORKER_CLASS="eventlet"
fi

# Install dependencies
echo "📥 Installing Python dependencies..."
if [ -f "$REQUIREMENTS_FILE" ]; then
    python3 -m pip install -r "$REQUIREMENTS_FILE"
else
    echo "⚠️ $REQUIREMENTS_FILE not found, using requirements.txt"
    python3 -m pip install -r requirements.txt
fi

# Install gunicorn with eventlet
echo "🔧 Installing gunicorn with eventlet..."
python3 -m pip install "gunicorn[eventlet]"

# Test critical imports
echo "🧪 Testing imports..."
python3 -c "import flask; print('✅ Flask OK')"
python3 -c "import pyrebase; print('✅ pyrebase OK')"
python3 -c "import eventlet; print('✅ eventlet OK')"
python3 -c "import gunicorn; print('✅ gunicorn OK')"

# Create runtime config
echo "⚙️ Creating runtime configuration..."
cat > runtime_config.env << EOF
WORKER_CLASS=$WORKER_CLASS
PYTHON_VERSION=$PYTHON_VERSION
REQUIREMENTS_FILE=$REQUIREMENTS_FILE
EOF

echo "✅ Build completed successfully!"
echo "📊 Configuration:"
echo "   Python: $PYTHON_VERSION"
echo "   Worker: $WORKER_CLASS"
echo "   Requirements: $REQUIREMENTS_FILE"
