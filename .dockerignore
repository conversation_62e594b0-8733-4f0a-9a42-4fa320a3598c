# Git
.git
.gitignore
.gitattributes

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Documentation
README.md
*.md
docs/

# Development files
.env.example
.env.local
.env.development

# Cache and logs
*.log
logs/
player_cache.json
cache/

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Build artifacts
build/
dist/
*.egg-info/

# Temporary files
tmp/
temp/
*.tmp
