#!/bin/bash
# Docker deployment script for CDN Video Player
# Optimized for production deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="cdn-video-player"
CONTAINER_NAME="cdn_video_player"
COMPOSE_FILE="docker-compose.yml"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking requirements..."

    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        log_info "Install Docker from: https://docs.docker.com/get-docker/"
        exit 1
    fi

    # Check for docker-compose or docker compose
    if command -v docker-compose &> /dev/null; then
        COMPOSE_CMD="docker-compose"
    elif docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
    else
        log_error "Docker Compose is not installed"
        log_info "Install Docker Compose from: https://docs.docker.com/compose/install/"
        exit 1
    fi
    
    if [ ! -f ".env" ]; then
        log_warning ".env file not found. Creating from template..."
        cat > .env << EOF
# Firebase Configuration
FIREBASE_DATABASE_URL=your_firebase_url_here
FIREBASE_API_KEY=your_firebase_api_key_here

# Google Drive API
DRIVE_API=your_google_drive_api_key_here

# Security
SECRET_KEY=$(openssl rand -hex 32)
EOF
        log_warning "Please edit .env file with your configuration"
    fi
    
    log_success "Requirements check passed"
}

build_image() {
    log_info "Building Docker image..."
    docker build -t $IMAGE_NAME . --no-cache
    log_success "Docker image built successfully"
}

deploy() {
    log_info "Deploying CDN Video Player..."

    # Stop existing containers
    $COMPOSE_CMD down 2>/dev/null || true

    # Start services
    $COMPOSE_CMD up -d
    
    # Wait for health check
    log_info "Waiting for service to be healthy..."
    sleep 10
    
    # Check if container is running
    if $COMPOSE_CMD ps | grep -q "Up"; then
        log_success "CDN Video Player deployed successfully!"
        log_info "Service is running on http://localhost:5000"

        # Show resource usage
        log_info "Container resource usage:"
        docker stats --no-stream $CONTAINER_NAME
    else
        log_error "Deployment failed"
        $COMPOSE_CMD logs
        exit 1
    fi
}

stop() {
    log_info "Stopping CDN Video Player..."
    $COMPOSE_CMD down
    log_success "Services stopped"
}

restart() {
    log_info "Restarting CDN Video Player..."
    $COMPOSE_CMD restart
    log_success "Services restarted"
}

logs() {
    $COMPOSE_CMD logs -f
}

status() {
    log_info "Service status:"
    $COMPOSE_CMD ps

    if $COMPOSE_CMD ps | grep -q "Up"; then
        log_info "Resource usage:"
        docker stats --no-stream $CONTAINER_NAME
    fi
}

cleanup() {
    log_info "Cleaning up Docker resources..."
    $COMPOSE_CMD down -v
    docker image rm $IMAGE_NAME 2>/dev/null || true
    docker system prune -f
    log_success "Cleanup completed"
}

# Main script
case "$1" in
    "build")
        check_requirements
        build_image
        ;;
    "deploy")
        check_requirements
        build_image
        deploy
        ;;
    "start")
        $COMPOSE_CMD up -d
        log_success "Services started"
        ;;
    "stop")
        stop
        ;;
    "restart")
        restart
        ;;
    "logs")
        logs
        ;;
    "status")
        status
        ;;
    "cleanup")
        cleanup
        ;;
    *)
        echo "Usage: $0 {build|deploy|start|stop|restart|logs|status|cleanup}"
        echo ""
        echo "Commands:"
        echo "  build   - Build Docker image"
        echo "  deploy  - Build and deploy the application"
        echo "  start   - Start existing containers"
        echo "  stop    - Stop containers"
        echo "  restart - Restart containers"
        echo "  logs    - Show container logs"
        echo "  status  - Show service status and resource usage"
        echo "  cleanup - Remove containers and images"
        exit 1
        ;;
esac
