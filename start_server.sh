#!/bin/bash
# CDN Video Player Startup Script

echo "🚀 Starting CDN Video Player..."

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install dependencies if needed
echo "📋 Checking dependencies..."
pip install -q flask pyrebase4 python-dotenv requests flask-cors pyjwt psutil

# Start the server
echo "🎬 Starting CDN Video Player on http://localhost:5000"
echo "📊 Database Status: http://localhost:5000/api/database_status"
echo "🔄 Database Refresh: POST http://localhost:5000/api/database_refresh"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

python3 cdn_player.py
