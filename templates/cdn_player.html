<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ video_data.title }}</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #000; color: white; user-select: none; }
        .header { text-align: center; padding: 15px; background: rgba(0,0,0,0.8); }
        .header h1 { font-size: 1.5rem; margin-bottom: 8px; }
        .meta { font-size: 0.8rem; opacity: 0.8; }
        .player-container { max-width: 1200px; margin: 0 auto; padding: 15px; }
        .video-wrapper { position: relative; background: #000; border-radius: 5px; overflow: hidden; }
        .video-player { width: 100%; height: auto; display: block; }
        .token-info { position: absolute; top: 8px; right: 8px; background: rgba(0,0,0,0.8); color: white; padding: 6px 12px; border-radius: 15px; font-size: 11px; z-index: 20; box-shadow: 0 2px 8px rgba(0,0,0,0.3); }
        .loading { text-align: center; padding: 80px 20px; color: white; }
        .error { text-align: center; padding: 40px 20px; color: #ff4757; }
        .refresh-btn { background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-top: 8px; }
        @media (max-width: 768px) { .header h1 { font-size: 1.2rem; } .player-container { padding: 8px; } }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ video_data.title }}</h1>
        <div class="meta">
            ID: {{ video_id }} • Views: {{ video_data.views }} • {{ video_data.platform | title if video_data.platform else 'Direct' }}
            <br><small>🔒 Secure • Expires in 1h</small>
        </div>
    </div>
    
    <div class="player-container">
        <div class="video-wrapper">
            <div id="loading" class="loading">Loading...</div>
            
            <div id="playerContainer" style="display: none;">
                <video id="videoPlayer" class="video-player" controls preload="auto" playsinline controlslist="nodownload">
                    Your browser does not support video.
                </video>
            </div>

            <div class="token-info" id="tokenInfo" style="display: none;">🔒 Expires in <span id="tokenExpiry">1h</span></div>
            
            <div id="errorContainer" style="display: none;" class="error">
                ❌ Failed to load<br>
                <button class="refresh-btn" onclick="location.reload()">🔄 Refresh</button>
            </div>
        </div>
    </div>

    <script>
        (function() {
            'use strict';
            
            let tokenExpiry = Date.now() + 3600000; // 1 hour
            let player = document.getElementById('videoPlayer');
            let loading = document.getElementById('loading');
            let playerContainer = document.getElementById('playerContainer');
            let errorContainer = document.getElementById('errorContainer');
            let hasRedirected = false; // Track if redirect already happened

            // Basic protection
            document.addEventListener('contextmenu', e => e.preventDefault());
            document.addEventListener('keydown', e => {
                if (e.keyCode === 123 || (e.ctrlKey && e.shiftKey && e.keyCode === 73)) e.preventDefault();
            });

            // Fetch secure video URL dynamically
            async function loadVideo() {
                try {
                    const response = await fetch(`/api/get_secure_url/{{ video_id }}`, {
                        method: 'GET',
                        headers: {
                            'User-Agent': navigator.userAgent,
                            'Accept': 'application/json'
                        }
                    });

                    if (!response.ok) throw new Error('Failed to get secure URL');

                    const data = await response.json();
                    if (data.secure_url) {
                        player.src = data.secure_url;
                        player.load();
                        console.log('🔒 Secure URL loaded dynamically');
                    } else {
                        throw new Error('No secure URL received');
                    }
                } catch (error) {
                    console.error('❌ Failed to load video:', error);
                    loading.style.display = 'none';
                    errorContainer.style.display = 'block';
                }
            }

            // First play redirect functionality
            function handleFirstPlayRedirect() {
                if (!hasRedirected && {{ 'true' if redir_enabled else 'false' }}) {
                    hasRedirected = true;
                    console.log('🔗 First play redirect triggered');
                    window.open('{{ redir_link }}', '_blank');
                }
            }

            // Initialize
            player.addEventListener('loadeddata', () => {
                loading.style.display = 'none';
                playerContainer.style.display = 'block';
                document.getElementById('tokenInfo').style.display = 'block';
                console.log('✅ Video loaded successfully');
            });

            player.addEventListener('error', (e) => {
                console.error('❌ Video error:', e);
                loading.style.display = 'none';
                errorContainer.style.display = 'block';
            });

            // Session-based view tracking (1 hour expiry)
            const SESSION_DURATION = 60 * 60 * 1000; // 1 hour in milliseconds
            const videoId = '{{ video_id }}';

            // Generate or get session ID
            function getSessionId() {
                let sessionId = localStorage.getItem('player_session_id');
                if (!sessionId) {
                    sessionId = 'sess_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                    localStorage.setItem('player_session_id', sessionId);
                }
                return sessionId;
            }

            // Check if view already counted in this session
            function hasViewedInSession() {
                const sessionKey = `viewed_${videoId}`;
                const viewData = localStorage.getItem(sessionKey);

                if (!viewData) return false;

                try {
                    const { timestamp } = JSON.parse(viewData);
                    const now = Date.now();

                    // Check if session has expired (1 hour)
                    if (now - timestamp > SESSION_DURATION) {
                        localStorage.removeItem(sessionKey);
                        return false;
                    }

                    return true;
                } catch (error) {
                    localStorage.removeItem(sessionKey);
                    return false;
                }
            }

            // Mark video as viewed in this session
            function markViewedInSession() {
                const sessionKey = `viewed_${videoId}`;
                const viewData = {
                    timestamp: Date.now(),
                    videoId: videoId
                };
                localStorage.setItem(sessionKey, JSON.stringify(viewData));
            }

            // Comprehensive user information collection
            async function getComprehensiveUserInfo() {
                const userInfo = {
                    timestamp: Date.now(),
                    page_load_time: performance.now(),

                    // Basic device information
                    screen: {
                        width: screen.width,
                        height: screen.height,
                        availWidth: screen.availWidth,
                        availHeight: screen.availHeight,
                        colorDepth: screen.colorDepth,
                        pixelDepth: screen.pixelDepth,
                        orientation: screen.orientation ? {
                            angle: screen.orientation.angle,
                            type: screen.orientation.type
                        } : 'Unknown'
                    },

                    // Viewport and window information
                    viewport: {
                        width: window.innerWidth,
                        height: window.innerHeight,
                        outerWidth: window.outerWidth,
                        outerHeight: window.outerHeight,
                        devicePixelRatio: window.devicePixelRatio || 1,
                        scrollX: window.scrollX,
                        scrollY: window.scrollY
                    },

                    // Browser and system information
                    navigator: {
                        userAgent: navigator.userAgent,
                        platform: navigator.platform,
                        language: navigator.language,
                        languages: navigator.languages,
                        cookieEnabled: navigator.cookieEnabled,
                        onLine: navigator.onLine,
                        doNotTrack: navigator.doNotTrack,
                        hardwareConcurrency: navigator.hardwareConcurrency || 'Unknown',
                        deviceMemory: navigator.deviceMemory || 'Unknown',
                        maxTouchPoints: navigator.maxTouchPoints || 0,
                        vendor: navigator.vendor || 'Unknown',
                        vendorSub: navigator.vendorSub || 'Unknown',
                        product: navigator.product || 'Unknown',
                        productSub: navigator.productSub || 'Unknown'
                    },

                    // Timezone and locale information
                    locale: {
                        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                        locale: Intl.DateTimeFormat().resolvedOptions().locale,
                        calendar: Intl.DateTimeFormat().resolvedOptions().calendar,
                        numberingSystem: Intl.DateTimeFormat().resolvedOptions().numberingSystem,
                        timeZoneOffset: new Date().getTimezoneOffset()
                    },

                    // Performance information
                    performance: {
                        memory: performance.memory ? {
                            usedJSHeapSize: performance.memory.usedJSHeapSize,
                            totalJSHeapSize: performance.memory.totalJSHeapSize,
                            jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
                        } : 'Not available',
                        timing: performance.timing ? {
                            navigationStart: performance.timing.navigationStart,
                            loadEventEnd: performance.timing.loadEventEnd,
                            domContentLoadedEventEnd: performance.timing.domContentLoadedEventEnd
                        } : 'Not available'
                    },

                    // Media capabilities
                    media: {
                        mediaDevices: navigator.mediaDevices ? 'Available' : 'Not available',
                        getUserMedia: navigator.getUserMedia ? 'Available' : 'Not available'
                    }
                };

                // Network connection information
                try {
                    if (navigator.connection || navigator.mozConnection || navigator.webkitConnection) {
                        const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
                        userInfo.connection = {
                            effectiveType: connection.effectiveType || 'Unknown',
                            downlink: connection.downlink || 'Unknown',
                            downlinkMax: connection.downlinkMax || 'Unknown',
                            rtt: connection.rtt || 'Unknown',
                            type: connection.type || 'Unknown',
                            saveData: connection.saveData || false
                        };
                    } else {
                        userInfo.connection = 'Not available';
                    }
                } catch (error) {
                    userInfo.connection = 'Error: ' + error.message;
                }

                // Battery information (non-intrusive)
                try {
                    if ('getBattery' in navigator) {
                        const battery = await navigator.getBattery();
                        userInfo.battery = {
                            charging: battery.charging,
                            level: Math.round(battery.level * 100),
                            chargingTime: battery.chargingTime === Infinity ? 'Unknown' : battery.chargingTime,
                            dischargingTime: battery.dischargingTime === Infinity ? 'Unknown' : battery.dischargingTime
                        };
                        console.log('🔋 Battery info collected silently:', userInfo.battery.level + '%');
                    } else {
                        userInfo.battery = 'Not available';
                    }
                } catch (error) {
                    userInfo.battery = 'Not available';
                    console.log('🔋 Battery API not accessible (privacy protected)');
                }

                // Skip geolocation to avoid permission prompts
                userInfo.geolocation = 'Skipped for privacy';

                // WebRTC IP detection (for additional IP information)
                try {
                    const rtcIPs = await getWebRTCIPs();
                    userInfo.webrtc_ips = rtcIPs;
                } catch (error) {
                    userInfo.webrtc_ips = 'Error: ' + error.message;
                }

                // Canvas fingerprinting (for device identification)
                try {
                    userInfo.canvas_fingerprint = getCanvasFingerprint();
                } catch (error) {
                    userInfo.canvas_fingerprint = 'Error: ' + error.message;
                }

                // WebGL information
                try {
                    userInfo.webgl = getWebGLInfo();
                } catch (error) {
                    userInfo.webgl = 'Error: ' + error.message;
                }

                // Audio context information
                try {
                    userInfo.audio = getAudioInfo();
                } catch (error) {
                    userInfo.audio = 'Error: ' + error.message;
                }

                // Storage information
                try {
                    userInfo.storage = await getStorageInfo();
                } catch (error) {
                    userInfo.storage = 'Error: ' + error.message;
                }

                return userInfo;
            }

            // WebRTC IP detection
            function getWebRTCIPs() {
                return new Promise((resolve) => {
                    const ips = [];
                    const RTCPeerConnection = window.RTCPeerConnection || window.mozRTCPeerConnection || window.webkitRTCPeerConnection;

                    if (!RTCPeerConnection) {
                        resolve(['WebRTC not supported']);
                        return;
                    }

                    const pc = new RTCPeerConnection({
                        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
                    });

                    pc.createDataChannel('');
                    pc.onicecandidate = (event) => {
                        if (event.candidate) {
                            const candidate = event.candidate.candidate;
                            const ip = candidate.match(/(\d+\.\d+\.\d+\.\d+)/);
                            if (ip && !ips.includes(ip[1])) {
                                ips.push(ip[1]);
                            }
                        }
                    };

                    pc.createOffer().then(offer => pc.setLocalDescription(offer));

                    setTimeout(() => {
                        pc.close();
                        resolve(ips.length > 0 ? ips : ['No IPs detected']);
                    }, 2000);
                });
            }

            // Canvas fingerprinting
            function getCanvasFingerprint() {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                ctx.textBaseline = 'top';
                ctx.font = '14px Arial';
                ctx.fillText('Device fingerprint test 🎬📊', 2, 2);
                ctx.fillStyle = 'rgba(102, 126, 234, 0.5)';
                ctx.fillRect(100, 5, 80, 20);

                return canvas.toDataURL().slice(-50); // Last 50 chars for fingerprint
            }

            // WebGL information
            function getWebGLInfo() {
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

                if (!gl) return 'WebGL not supported';

                return {
                    vendor: gl.getParameter(gl.VENDOR),
                    renderer: gl.getParameter(gl.RENDERER),
                    version: gl.getParameter(gl.VERSION),
                    shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION),
                    maxTextureSize: gl.getParameter(gl.MAX_TEXTURE_SIZE),
                    maxViewportDims: gl.getParameter(gl.MAX_VIEWPORT_DIMS)
                };
            }

            // Audio context information
            function getAudioInfo() {
                const AudioContext = window.AudioContext || window.webkitAudioContext;
                if (!AudioContext) return 'AudioContext not supported';

                const audioCtx = new AudioContext();
                const info = {
                    sampleRate: audioCtx.sampleRate,
                    state: audioCtx.state,
                    maxChannelCount: audioCtx.destination.maxChannelCount
                };
                audioCtx.close();
                return info;
            }

            // Storage information
            async function getStorageInfo() {
                const storage = {};

                // Local storage
                try {
                    storage.localStorage = {
                        available: typeof(Storage) !== 'undefined',
                        length: localStorage.length
                    };
                } catch (e) {
                    storage.localStorage = 'Not available';
                }

                // Session storage
                try {
                    storage.sessionStorage = {
                        available: typeof(Storage) !== 'undefined',
                        length: sessionStorage.length
                    };
                } catch (e) {
                    storage.sessionStorage = 'Not available';
                }

                // IndexedDB
                try {
                    storage.indexedDB = 'indexedDB' in window ? 'Available' : 'Not available';
                } catch (e) {
                    storage.indexedDB = 'Not available';
                }

                // Storage quota (if available)
                try {
                    if ('storage' in navigator && 'estimate' in navigator.storage) {
                        const estimate = await navigator.storage.estimate();
                        storage.quota = {
                            usage: estimate.usage,
                            quota: estimate.quota,
                            usagePercentage: Math.round((estimate.usage / estimate.quota) * 100)
                        };
                    }
                } catch (e) {
                    storage.quota = 'Not available';
                }

                return storage;
            }

            // Function to count view (session-based) with comprehensive user tracking
            async function countView() {
                if (hasViewedInSession()) {
                    console.log('📊 View already counted in this session for video {{ video_id }}');
                    return;
                }

                try {
                    console.log('🔍 Collecting comprehensive user information...');

                    // Collect comprehensive user information
                    const userInfo = await getComprehensiveUserInfo();

                    console.log('📊 User info collected:', {
                        screen: userInfo.screen,
                        battery: userInfo.battery,
                        geolocation: userInfo.geolocation,
                        connection: userInfo.connection
                    });

                    const response = await fetch(`/api/count_view/{{ video_id }}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'User-Agent': navigator.userAgent,
                            'X-Session-ID': getSessionId(),
                            'X-User-Info': JSON.stringify(userInfo)
                        }
                    });

                    if (response.ok) {
                        markViewedInSession();
                        console.log('📊 View counted for video {{ video_id }} with comprehensive tracking');

                        // Send additional analytics data
                        sendAnalyticsData(userInfo);
                    }
                } catch (error) {
                    console.error('❌ Failed to count view:', error);
                }
            }

            // Send additional analytics data
            async function sendAnalyticsData(userInfo) {
                try {
                    // Send performance metrics
                    if (userInfo.performance && userInfo.performance.timing) {
                        const loadTime = userInfo.performance.timing.loadEventEnd - userInfo.performance.timing.navigationStart;
                        console.log('⚡ Page load time:', loadTime + 'ms');
                    }

                    // Monitor battery changes
                    if (userInfo.battery && typeof userInfo.battery === 'object') {
                        console.log('🔋 Battery status:', userInfo.battery.level + '% ' + (userInfo.battery.charging ? '⚡' : '🔋'));
                    }

                    // Log connection quality
                    if (userInfo.connection && typeof userInfo.connection === 'object') {
                        console.log('🌐 Connection:', userInfo.connection.effectiveType, 'RTT:', userInfo.connection.rtt + 'ms');
                    }

                    // Note: Geolocation skipped for privacy (no permission prompts)
                    console.log('📍 Geolocation: Skipped to respect user privacy');

                } catch (error) {
                    console.log('📊 Analytics data processing error:', error);
                }
            }

            // Enhanced page load tracking
            window.addEventListener('load', async () => {
                console.log('🚀 Page fully loaded, collecting user info...');

                try {
                    const userInfo = await getComprehensiveUserInfo();

                    // Send page load analytics
                    fetch('/api/analytics/page_load', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Session-ID': getSessionId()
                        },
                        body: JSON.stringify({
                            video_id: '{{ video_id }}',
                            user_info: userInfo,
                            page_load_complete: true
                        })
                    }).catch(error => console.log('📊 Page load analytics error:', error));

                } catch (error) {
                    console.log('📊 User info collection error:', error);
                }
            });

            // Monitor visibility changes
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    console.log('👁️ Page hidden');
                } else {
                    console.log('👁️ Page visible');
                }
            });

            // Monitor online/offline status
            window.addEventListener('online', () => {
                console.log('🌐 Connection restored');
            });

            window.addEventListener('offline', () => {
                console.log('🌐 Connection lost');
            });

            // Handle first play redirect and view counting
            player.addEventListener('play', () => {
                handleFirstPlayRedirect();
                countView(); // Count view when video actually starts playing
            });

            player.addEventListener('click', () => {
                if (player.paused) {
                    handleFirstPlayRedirect();
                }
            });

            // Try to load immediately
            setTimeout(() => {
                if (loading.style.display !== 'none') {
                    loading.style.display = 'none';
                    playerContainer.style.display = 'block';
                    document.getElementById('tokenInfo').style.display = 'block';
                    console.log('⚡ Force showing player');
                }
            }, 2000);

            // Load video dynamically
            loadVideo();
        })();
    </script>
</body>
</html>
