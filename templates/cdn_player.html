<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ video_data.title }}</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #000; color: white; user-select: none; }
        .header { text-align: center; padding: 15px; background: rgba(0,0,0,0.8); }
        .header h1 { font-size: 1.5rem; margin-bottom: 8px; }
        .meta { font-size: 0.8rem; opacity: 0.8; }
        .player-container { max-width: 1200px; margin: 0 auto; padding: 15px; }
        .video-wrapper { position: relative; background: #000; border-radius: 5px; overflow: hidden; }
        .video-player { width: 100%; height: auto; display: block; }
        .token-info { position: absolute; top: 8px; right: 8px; background: rgba(0,0,0,0.8); color: white; padding: 6px 12px; border-radius: 15px; font-size: 11px; z-index: 20; box-shadow: 0 2px 8px rgba(0,0,0,0.3); }
        .loading { text-align: center; padding: 80px 20px; color: white; }
        .error { text-align: center; padding: 40px 20px; color: #ff4757; }
        .refresh-btn { background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-top: 8px; }
        @media (max-width: 768px) { .header h1 { font-size: 1.2rem; } .player-container { padding: 8px; } }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ video_data.title }}</h1>
        <div class="meta">
            ID: {{ video_id }} • Views: {{ video_data.views }} • {{ video_data.platform | title if video_data.platform else 'Direct' }}
            <br><small>🔒 Secure • Expires in 1h</small>
        </div>
    </div>
    
    <div class="player-container">
        <div class="video-wrapper">
            <div id="loading" class="loading">Loading...</div>
            
            <div id="playerContainer" style="display: none;">
                <video id="videoPlayer" class="video-player" controls preload="auto" playsinline controlslist="nodownload">
                    Your browser does not support video.
                </video>
            </div>

            <div class="token-info" id="tokenInfo" style="display: none;">🔒 Expires in <span id="tokenExpiry">1h</span></div>
            
            <div id="errorContainer" style="display: none;" class="error">
                ❌ Failed to load<br>
                <button class="refresh-btn" onclick="location.reload()">🔄 Refresh</button>
            </div>
        </div>
    </div>

    <script>
        (function() {
            'use strict';
            
            let tokenExpiry = Date.now() + 3600000; // 1 hour
            let player = document.getElementById('videoPlayer');
            let loading = document.getElementById('loading');
            let playerContainer = document.getElementById('playerContainer');
            let errorContainer = document.getElementById('errorContainer');
            let hasRedirected = false; // Track if redirect already happened

            // Basic protection
            document.addEventListener('contextmenu', e => e.preventDefault());
            document.addEventListener('keydown', e => {
                if (e.keyCode === 123 || (e.ctrlKey && e.shiftKey && e.keyCode === 73)) e.preventDefault();
            });

            // Fetch secure video URL dynamically
            async function loadVideo() {
                try {
                    const response = await fetch(`/api/get_secure_url/{{ video_id }}`, {
                        method: 'GET',
                        headers: {
                            'User-Agent': navigator.userAgent,
                            'Accept': 'application/json'
                        }
                    });

                    if (!response.ok) throw new Error('Failed to get secure URL');

                    const data = await response.json();
                    if (data.secure_url) {
                        player.src = data.secure_url;
                        player.load();
                        console.log('🔒 Secure URL loaded dynamically');
                    } else {
                        throw new Error('No secure URL received');
                    }
                } catch (error) {
                    console.error('❌ Failed to load video:', error);
                    loading.style.display = 'none';
                    errorContainer.style.display = 'block';
                }
            }

            // First play redirect functionality
            function handleFirstPlayRedirect() {
                if (!hasRedirected && {{ 'true' if redir_enabled else 'false' }}) {
                    hasRedirected = true;
                    console.log('🔗 First play redirect triggered');
                    window.open('{{ redir_link }}', '_blank');
                }
            }

            // Initialize
            player.addEventListener('loadeddata', () => {
                loading.style.display = 'none';
                playerContainer.style.display = 'block';
                document.getElementById('tokenInfo').style.display = 'block';
                console.log('✅ Video loaded successfully');
            });

            player.addEventListener('error', (e) => {
                console.error('❌ Video error:', e);
                loading.style.display = 'none';
                errorContainer.style.display = 'block';
            });

            // Session-based view tracking (1 hour expiry)
            const SESSION_DURATION = 60 * 60 * 1000; // 1 hour in milliseconds
            const videoId = '{{ video_id }}';

            // Generate or get session ID
            function getSessionId() {
                let sessionId = localStorage.getItem('player_session_id');
                if (!sessionId) {
                    sessionId = 'sess_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                    localStorage.setItem('player_session_id', sessionId);
                }
                return sessionId;
            }

            // Check if view already counted in this session
            function hasViewedInSession() {
                const sessionKey = `viewed_${videoId}`;
                const viewData = localStorage.getItem(sessionKey);

                if (!viewData) return false;

                try {
                    const { timestamp } = JSON.parse(viewData);
                    const now = Date.now();

                    // Check if session has expired (1 hour)
                    if (now - timestamp > SESSION_DURATION) {
                        localStorage.removeItem(sessionKey);
                        return false;
                    }

                    return true;
                } catch (error) {
                    localStorage.removeItem(sessionKey);
                    return false;
                }
            }

            // Mark video as viewed in this session
            function markViewedInSession() {
                const sessionKey = `viewed_${videoId}`;
                const viewData = {
                    timestamp: Date.now(),
                    videoId: videoId
                };
                localStorage.setItem(sessionKey, JSON.stringify(viewData));
            }

            // Function to count view (session-based)
            async function countView() {
                if (hasViewedInSession()) {
                    console.log('📊 View already counted in this session for video {{ video_id }}');
                    return;
                }

                try {
                    const response = await fetch(`/api/count_view/{{ video_id }}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'User-Agent': navigator.userAgent,
                            'X-Session-ID': getSessionId()
                        }
                    });

                    if (response.ok) {
                        markViewedInSession();
                        console.log('📊 View counted for video {{ video_id }} (session-based)');
                    }
                } catch (error) {
                    console.error('❌ Failed to count view:', error);
                }
            }

            // Handle first play redirect and view counting
            player.addEventListener('play', () => {
                handleFirstPlayRedirect();
                countView(); // Count view when video actually starts playing
            });

            player.addEventListener('click', () => {
                if (player.paused) {
                    handleFirstPlayRedirect();
                }
            });

            // Try to load immediately
            setTimeout(() => {
                if (loading.style.display !== 'none') {
                    loading.style.display = 'none';
                    playerContainer.style.display = 'block';
                    document.getElementById('tokenInfo').style.display = 'block';
                    console.log('⚡ Force showing player');
                }
            }, 2000);

            // Load video dynamically
            loadVideo();
        })();
    </script>
</body>
</html>
