<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CDN Video Player</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';">
    <meta name="robots" content="noindex, nofollow, noarchive, nosnippet, noimageindex">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .upload-form {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            background: rgba(255,255,255,0.9);
            color: #333;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(255,255,255,0.3);
        }
        
        .submit-btn {
            width: 100%;
            padding: 18px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        
        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .result {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            display: none;
        }
        
        .result.success {
            border-left: 5px solid #2ed573;
        }
        
        .result.error {
            border-left: 5px solid #ff4757;
        }
        
        .short-url {
            background: rgba(0,0,0,0.2);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 1.1rem;
            word-break: break-all;
        }
        
        .copy-btn {
            background: #2ed573;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .stats {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-item {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #ffd700;
        }
        
        .supported-formats {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
        }
        
        .format-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        
        .format-tag {
            background: rgba(255,255,255,0.2);
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .header h1 { font-size: 2rem; }
            .upload-form { padding: 25px; }
            .container { padding: 20px 15px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 CDN Video Player</h1>
            <p>Share any video with a short, secure link</p>
        </div>
        
        <div class="upload-form">
            <form id="videoForm">
                <div class="form-group">
                    <label for="videoUrl">Video URL *</label>
                    <input type="url" id="videoUrl" placeholder="https://example.com/video.mp4" required>
                </div>
                
                <div class="form-group">
                    <label for="videoTitle">Title (Optional)</label>
                    <input type="text" id="videoTitle" placeholder="My awesome video">
                </div>
                
                <div class="form-group">
                    <label for="expireDays">Expire After</label>
                    <select id="expireDays">
                        <option value="1">1 Day</option>
                        <option value="3">3 Days</option>
                        <option value="7">1 Week</option>
                        <option value="14">2 Weeks</option>
                        <option value="30" selected>1 Month (Default)</option>
                        <option value="90">3 Months</option>
                        <option value="365">1 Year</option>
                    </select>
                </div>
                
                <button type="submit" class="submit-btn" id="submitBtn">
                    🚀 Generate Short Link
                </button>
            </form>
        </div>
        
        <div id="result" class="result">
            <div id="resultContent"></div>
        </div>
        
        <div class="supported-formats">
            <h3>📋 Supported Platforms & Formats</h3>
            <div class="format-list">
                <span class="format-tag">📁 Google Drive</span>
                <span class="format-tag">📦 Catbox.moe</span>
                <span class="format-tag">🖼️ Imgur</span>
                <span class="format-tag">🎬 Streamable</span>
                <span class="format-tag">🎭 Gfycat</span>
                <span class="format-tag">🤖 Reddit (v.redd.it)</span>
                <span class="format-tag">💬 Discord CDN</span>
                <span class="format-tag">🎥 Direct Video Files (cdn, github, others)</span>
            </div>
            <div style="margin-top: 15px; font-size: 0.9rem; opacity: 0.8;">
                <strong>Supported formats:</strong> .mp4, .webm, .avi, .mov, .mkv, .m4v, .flv, .wmv
            </div>
            <div style="margin-top: 10px; font-size: 0.9rem; opacity: 0.8;">
                ✅ Enhanced seeking • 🔄 Auto-retry on errors • 🎯 Platform-optimized playback • 🎵 Background play support
            </div>
        </div>
        
        <div class="stats">
            <h3>📊 Platform Statistics</h3>
            <div class="stats-grid" id="statsGrid">
                <div class="stat-item">
                    <div class="stat-number" id="totalVideos">-</div>
                    <div>Total Videos</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="activeVideos">-</div>
                    <div>Active Videos</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalViews">-</div>
                    <div>Total Views</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Load statistics
        async function loadStats() {
            try {
                const response = await fetch('/api/stats');
                const stats = await response.json();
                
                document.getElementById('totalVideos').textContent = stats.total_videos;
                document.getElementById('activeVideos').textContent = stats.active_videos;
                document.getElementById('totalViews').textContent = stats.total_views;
            } catch (error) {
                console.error('Failed to load stats:', error);
            }
        }
        
        // Handle form submission
        document.getElementById('videoForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const result = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            
            // Get form data
            const videoUrl = document.getElementById('videoUrl').value.trim();
            const videoTitle = document.getElementById('videoTitle').value.trim();
            const expireDays = parseInt(document.getElementById('expireDays').value);
            
            // Disable submit button
            submitBtn.disabled = true;
            submitBtn.textContent = '⏳ Processing...';
            
            try {
                const response = await fetch('/api/submit_video', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        url: videoUrl,
                        title: videoTitle,
                        expire_days: expireDays
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    result.className = 'result success';
                    result.style.display = 'block';
                    
                    resultContent.innerHTML = `
                        <h3>✅ Success! Your video link is ready</h3>
                        <div class="short-url">
                            ${data.short_url}
                            <button class="copy-btn" onclick="copyToClipboard('${data.short_url}')">📋 Copy</button>
                        </div>
                        <p><strong>Video ID:</strong> ${data.video_id}</p>
                        <p><strong>Expires:</strong> ${new Date(data.expires_at).toLocaleString()}</p>
                        <p><strong>Duration:</strong> ${data.expire_days} days</p>
                        <br>
                        <a href="${data.short_url}" target="_blank" style="color: #ffd700; text-decoration: none;">
                            🎬 Watch Video →
                        </a>
                    `;
                    
                    // Reset form
                    document.getElementById('videoForm').reset();
                    
                    // Reload stats
                    loadStats();
                } else {
                    throw new Error(data.error || 'Unknown error');
                }
                
            } catch (error) {
                result.className = 'result error';
                result.style.display = 'block';
                
                resultContent.innerHTML = `
                    <h3>❌ Error</h3>
                    <p>${error.message}</p>
                `;
            } finally {
                // Re-enable submit button
                submitBtn.disabled = false;
                submitBtn.textContent = '🚀 Generate Short Link';
            }
        });
        
        // Copy to clipboard function
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                // Show feedback
                const btn = event.target;
                const originalText = btn.textContent;
                btn.textContent = '✅ Copied!';
                setTimeout(() => {
                    btn.textContent = originalText;
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy:', err);
            });
        }
        
        // Load stats on page load
        loadStats();

        // Auto-refresh stats every 30 seconds
        setInterval(loadStats, 30000);
    </script>

    <!-- Anti-Scraping Protection -->
    <script>
        (function() {
            'use strict';

            // Configuration
            const config = {
                devToolsDetection: true,
                rightClickDisabled: true,
                keyboardShortcutsDisabled: true,
                textSelectionDisabled: true,
                dragDropDisabled: true,
                printDisabled: true,
                consoleWarnings: true
            };

            // DevTools detection
            let devtools = { open: false };
            const threshold = 160;

            function detectDevTools() {
                if (config.devToolsDetection) {
                    const widthThreshold = window.outerWidth - window.innerWidth > threshold;
                    const heightThreshold = window.outerHeight - window.innerHeight > threshold;

                    if (!(heightThreshold && widthThreshold) &&
                        ((window.Firebug && window.Firebug.chrome && window.Firebug.chrome.isInitialized) ||
                         widthThreshold || heightThreshold)) {

                        if (!devtools.open) {
                            devtools.open = true;
                            onDevToolsOpen();
                        }
                    } else {
                        devtools.open = false;
                    }
                }
            }

            function onDevToolsOpen() {
                document.body.innerHTML = `
                    <div style="
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: #000;
                        color: #fff;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-family: Arial, sans-serif;
                        font-size: 24px;
                        z-index: 9999;
                    ">
                        <div style="text-align: center;">
                            <h1>🚫 Access Denied</h1>
                            <p>Developer tools detected. Please close them to continue.</p>
                        </div>
                    </div>
                `;
            }

            // Disable right-click
            if (config.rightClickDisabled) {
                document.addEventListener('contextmenu', function(e) {
                    e.preventDefault();
                    return false;
                });
            }

            // Disable text selection
            if (config.textSelectionDisabled) {
                document.addEventListener('selectstart', function(e) {
                    if (e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
                        e.preventDefault();
                        return false;
                    }
                });
            }

            // Disable drag and drop
            if (config.dragDropDisabled) {
                document.addEventListener('dragstart', function(e) {
                    e.preventDefault();
                    return false;
                });
            }

            // Disable keyboard shortcuts
            if (config.keyboardShortcutsDisabled) {
                document.addEventListener('keydown', function(e) {
                    // Disable F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U, Ctrl+S, Ctrl+A, Ctrl+P
                    if (e.keyCode === 123 || // F12
                        (e.ctrlKey && e.shiftKey && e.keyCode === 73) || // Ctrl+Shift+I
                        (e.ctrlKey && e.shiftKey && e.keyCode === 74) || // Ctrl+Shift+J
                        (e.ctrlKey && e.keyCode === 85) || // Ctrl+U
                        (e.ctrlKey && e.keyCode === 83) || // Ctrl+S
                        (e.ctrlKey && e.keyCode === 65) || // Ctrl+A
                        (e.ctrlKey && e.keyCode === 80) || // Ctrl+P
                        (e.ctrlKey && e.keyCode === 67) || // Ctrl+C
                        (e.ctrlKey && e.keyCode === 86) || // Ctrl+V
                        (e.ctrlKey && e.keyCode === 88)) { // Ctrl+X

                        // Allow these keys in input fields
                        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                            if (e.keyCode === 67 || e.keyCode === 86 || e.keyCode === 88 || e.keyCode === 65) {
                                return true; // Allow copy/paste/cut/select all in inputs
                            }
                        }

                        e.preventDefault();
                        return false;
                    }
                });
            }

            // Disable printing
            if (config.printDisabled) {
                window.addEventListener('beforeprint', function(e) {
                    e.preventDefault();
                    return false;
                });

                window.print = function() {
                    return false;
                };
            }

            // Console warnings
            if (config.consoleWarnings) {
                const warning = `
%c🚫 STOP! 🚫
%cThis is a browser feature intended for developers.
If someone told you to copy-paste something here, it's a scam.
Unauthorized access to this content is prohibited.
                `;

                console.log(warning,
                    'color: red; font-size: 50px; font-weight: bold;',
                    'color: red; font-size: 16px;'
                );

                // Clear console periodically
                setInterval(() => {
                    console.clear();
                    console.log(warning,
                        'color: red; font-size: 50px; font-weight: bold;',
                        'color: red; font-size: 16px;'
                    );
                }, 5000);
            }

            // Detect automation tools
            function detectAutomation() {
                if (navigator.webdriver ||
                    window.phantom ||
                    window._phantom ||
                    window.callPhantom ||
                    window.chrome && window.chrome.runtime && window.chrome.runtime.onConnect ||
                    window.selenium ||
                    document.$cdc_asdjflasutopfhvcZLmcfl_) {

                    document.body.innerHTML = `
                        <div style="
                            position: fixed;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            background: #000;
                            color: #fff;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-family: Arial, sans-serif;
                            font-size: 24px;
                            z-index: 9999;
                        ">
                            <div style="text-align: center;">
                                <h1>🤖 Automation Detected</h1>
                                <p>Automated access is not allowed.</p>
                            </div>
                        </div>
                    `;
                    return true;
                }
                return false;
            }

            // Blur detection
            function setupBlurDetection() {
                document.addEventListener('visibilitychange', function() {
                    if (document.hidden) {
                        // Page is hidden, could be suspicious
                        console.log('Page visibility changed');
                    }
                });
            }

            // Initialize protection
            function initializeProtection() {
                if (detectAutomation()) {
                    return;
                }

                setupBlurDetection();

                // Start DevTools detection
                setInterval(detectDevTools, 500);

                // Prevent framing
                if (window.top !== window.self) {
                    window.top.location = window.self.location;
                }

                // Prevent screenshot tools
                document.addEventListener('keyup', function(e) {
                    if (e.key === 'PrintScreen') {
                        navigator.clipboard.writeText('Screenshot disabled for security reasons.');
                    }
                });
            }

            // Initialize when DOM is ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initializeProtection);
            } else {
                initializeProtection();
            }

            // Prevent script tampering
            Object.freeze(config);

        })();
    </script>
</body>
</html>
