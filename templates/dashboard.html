<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f6fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 1.8rem;
        }
        
        .header-actions {
            display: flex;
            gap: 15px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-secondary {
            background: rgba(255,255,255,0.2);
            color: white;
        }
        
        .btn-danger {
            background: #ff4757;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .dashboard-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .section-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
            color: #333;
        }
        
        .section-content {
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .activity-item, .session-item, .ip-item {
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .activity-item:last-child, .session-item:last-child, .ip-item:last-child {
            border-bottom: none;
        }
        
        .activity-info, .session-info, .ip-info {
            flex: 1;
        }
        
        .activity-video, .session-video {
            font-weight: 600;
            color: #667eea;
        }
        
        .activity-ip, .session-ip, .ip-address {
            color: #666;
            font-size: 0.9rem;
        }
        
        .activity-time, .session-time {
            color: #999;
            font-size: 0.8rem;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-playing {
            background: #2ed573;
            color: white;
        }
        
        .status-loading {
            background: #ffa502;
            color: white;
        }
        
        .ip-stats {
            display: flex;
            gap: 15px;
            font-size: 0.9rem;
        }
        
        .ip-stat {
            color: #666;
        }
        
        .no-data {
            text-align: center;
            color: #999;
            padding: 40px 20px;
            font-style: italic;
        }
        
        .refresh-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #2ed573;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 0.9rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .refresh-indicator.show {
            opacity: 1;
        }
        
        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .header-content {
                flex-direction: column;
                gap: 15px;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>🎬 Admin Dashboard</h1>
            <div class="header-actions">
                <button class="btn btn-secondary" onclick="refreshData()">🔄 Refresh</button>
                <button class="btn btn-danger" onclick="clearLogs()">🗑️ Clear Logs</button>
                <a href="/me/dash/logout" class="btn btn-secondary">🚪 Logout</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="activeSessions">-</div>
                <div class="stat-label">Active Sessions</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalIPs">-</div>
                <div class="stat-label">Unique IPs</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalActivities">-</div>
                <div class="stat-label">Total Activities</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="recentActivities">-</div>
                <div class="stat-label">Recent Activities (24h)</div>
            </div>
        </div>
        
        <div class="dashboard-grid">
            <div class="dashboard-section">
                <div class="section-header">
                    ⚡ Active Sessions
                </div>
                <div class="section-content" id="activeSessionsContent">
                    <div class="no-data">Loading...</div>
                </div>
            </div>
            
            <div class="dashboard-section">
                <div class="section-header">
                    📊 Recent Activity
                </div>
                <div class="section-content" id="recentActivityContent">
                    <div class="no-data">Loading...</div>
                </div>
            </div>
        </div>
        
        <div class="dashboard-section">
            <div class="section-header">
                🌐 Top IP Addresses
            </div>
            <div class="section-content" id="topIPsContent">
                <div class="no-data">Loading...</div>
            </div>
        </div>
    </div>
    
    <div class="refresh-indicator" id="refreshIndicator">
        📊 Data refreshed
    </div>
    
    <script>
        let refreshInterval;
        
        // Format timestamp to readable time
        function formatTime(timestamp) {
            const date = new Date(timestamp * 1000);
            return date.toLocaleTimeString();
        }
        
        // Format relative time
        function formatRelativeTime(timestamp) {
            const now = Date.now() / 1000;
            const diff = now - timestamp;
            
            if (diff < 60) return 'Just now';
            if (diff < 3600) return `${Math.floor(diff / 60)}m ago`;
            if (diff < 86400) return `${Math.floor(diff / 3600)}h ago`;
            return `${Math.floor(diff / 86400)}d ago`;
        }
        
        // Load dashboard data
        async function loadDashboardData() {
            try {
                const response = await fetch('/api/dashboard/stats');
                const data = await response.json();
                
                // Update stats
                document.getElementById('activeSessions').textContent = data.active_sessions.length;
                document.getElementById('totalIPs').textContent = data.total_unique_ips;
                document.getElementById('totalActivities').textContent = data.total_activities;
                document.getElementById('recentActivities').textContent = data.recent_activity.length;
                
                // Update active sessions
                updateActiveSessions(data.active_sessions);
                
                // Update recent activity
                updateRecentActivity(data.recent_activity);
                
                // Update top IPs
                updateTopIPs(data.top_ips);
                
            } catch (error) {
                console.error('Failed to load dashboard data:', error);
            }
        }
        
        // Update active sessions display
        function updateActiveSessions(sessions) {
            const container = document.getElementById('activeSessionsContent');
            
            if (sessions.length === 0) {
                container.innerHTML = '<div class="no-data">No active sessions</div>';
                return;
            }
            
            container.innerHTML = sessions.map(session => `
                <div class="session-item">
                    <div class="session-info">
                        <div class="session-video">📺 ${session.video_id}</div>
                        <div class="session-ip">IP: ${session.ip_address}</div>
                        <div class="session-time">${formatRelativeTime(session.timestamp)}</div>
                    </div>
                    <div class="status-badge status-${session.status}">
                        ${session.status}
                    </div>
                </div>
            `).join('');
        }
        
        // Update recent activity display
        function updateRecentActivity(activities) {
            const container = document.getElementById('recentActivityContent');
            
            if (activities.length === 0) {
                container.innerHTML = '<div class="no-data">No recent activity</div>';
                return;
            }
            
            container.innerHTML = activities.reverse().map(activity => `
                <div class="activity-item">
                    <div class="activity-info">
                        <div class="activity-video">🎥 ${activity.video_id}</div>
                        <div class="activity-ip">IP: ${activity.ip_address}</div>
                        <div class="activity-time">${formatTime(activity.timestamp)} - ${activity.action}</div>
                    </div>
                </div>
            `).join('');
        }
        
        // Update top IPs display
        function updateTopIPs(ips) {
            const container = document.getElementById('topIPsContent');
            
            if (ips.length === 0) {
                container.innerHTML = '<div class="no-data">No IP data</div>';
                return;
            }
            
            container.innerHTML = ips.map(ip => `
                <div class="ip-item">
                    <div class="ip-info">
                        <div class="ip-address">🌐 ${ip.ip}</div>
                        <div class="ip-stats">
                            <span class="ip-stat">👁️ ${ip.total_views} views</span>
                            <span class="ip-stat">🎬 ${ip.unique_videos} videos</span>
                            <span class="ip-stat">⏰ ${formatRelativeTime(ip.last_seen)}</span>
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        // Refresh data manually
        function refreshData() {
            loadDashboardData();
            showRefreshIndicator();
        }
        
        // Show refresh indicator
        function showRefreshIndicator() {
            const indicator = document.getElementById('refreshIndicator');
            indicator.classList.add('show');
            setTimeout(() => {
                indicator.classList.remove('show');
            }, 2000);
        }
        
        // Clear logs
        async function clearLogs() {
            if (confirm('Are you sure you want to clear all logs?')) {
                try {
                    await fetch('/api/dashboard/clear_logs');
                    loadDashboardData();
                    alert('Logs cleared successfully');
                } catch (error) {
                    alert('Failed to clear logs');
                }
            }
        }
        
        // Initialize dashboard
        loadDashboardData();
        
        // Auto-refresh every 10 seconds
        refreshInterval = setInterval(loadDashboardData, 10000);
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        });
    </script>
</body>
</html>
