<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f6fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 1.8rem;
        }
        
        .header-actions {
            display: flex;
            gap: 15px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-secondary {
            background: rgba(255,255,255,0.2);
            color: white;
        }
        
        .btn-danger {
            background: #ff4757;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .dashboard-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .section-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
            color: #333;
        }
        
        .section-content {
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .activity-item, .session-item, .ip-item {
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .activity-item:last-child, .session-item:last-child, .ip-item:last-child {
            border-bottom: none;
        }
        
        .activity-info, .session-info, .ip-info {
            flex: 1;
        }
        
        .activity-video, .session-video {
            font-weight: 600;
            color: #667eea;
        }
        
        .activity-ip, .session-ip, .ip-address {
            color: #666;
            font-size: 0.9rem;
        }
        
        .activity-time, .session-time {
            color: #999;
            font-size: 0.8rem;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-playing {
            background: #2ed573;
            color: white;
        }
        
        .status-loading {
            background: #ffa502;
            color: white;
        }
        
        .ip-stats {
            display: flex;
            gap: 15px;
            font-size: 0.9rem;
        }
        
        .ip-stat {
            color: #666;
        }
        
        .no-data {
            text-align: center;
            color: #999;
            padding: 40px 20px;
            font-style: italic;
        }
        
        .refresh-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #2ed573;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 0.9rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .refresh-indicator.show {
            opacity: 1;
        }

        .ip-location, .ip-device, .ip-battery {
            color: #666;
            font-size: 0.85rem;
            margin: 2px 0;
        }

        .video-item, .geo-item {
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .video-item:last-child, .geo-item:last-child {
            border-bottom: none;
        }

        .video-id, .geo-country {
            font-weight: 600;
            color: #667eea;
            margin-bottom: 5px;
        }

        .video-stats, .geo-stats {
            display: flex;
            gap: 15px;
            font-size: 0.9rem;
            flex-wrap: wrap;
        }

        .video-stat, .geo-stat {
            color: #666;
        }

        .device-stat-item {
            padding: 5px 0;
            color: #666;
            font-size: 0.9rem;
        }

        .hour-stat {
            display: grid;
            grid-template-columns: 60px 1fr 40px;
            gap: 10px;
            align-items: center;
            padding: 5px 0;
        }

        .hour-label {
            font-size: 0.9rem;
            color: #666;
        }

        .hour-bar {
            background: #f0f0f0;
            border-radius: 3px;
            height: 20px;
            position: relative;
        }

        .hour-count {
            font-size: 0.9rem;
            color: #333;
            text-align: right;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .video-stats, .geo-stats {
                flex-direction: column;
                gap: 5px;
            }

            .hour-stat {
                grid-template-columns: 50px 1fr 30px;
                gap: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>🎬 Admin Dashboard</h1>
            <div class="header-actions">
                <button class="btn btn-secondary" onclick="refreshData()">🔄 Refresh</button>
                <button class="btn btn-danger" onclick="clearLogs()">🗑️ Clear Logs</button>
                <a href="/me/dash/logout" class="btn btn-secondary">🚪 Logout</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="activeSessions">-</div>
                <div class="stat-label">Active Sessions</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalIPs">-</div>
                <div class="stat-label">Unique IPs</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalActivities">-</div>
                <div class="stat-label">Total Activities</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="recentActivities">-</div>
                <div class="stat-label">Recent Activities (24h)</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="topCountry">-</div>
                <div class="stat-label">Top Country</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="topBrowser">-</div>
                <div class="stat-label">Top Browser</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="mobileUsers">-</div>
                <div class="stat-label">Mobile Users %</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="topVideo">-</div>
                <div class="stat-label">Most Watched Video</div>
            </div>
        </div>
        
        <div class="dashboard-grid">
            <div class="dashboard-section">
                <div class="section-header">
                    ⚡ Active Sessions
                </div>
                <div class="section-content" id="activeSessionsContent">
                    <div class="no-data">Loading...</div>
                </div>
            </div>
            
            <div class="dashboard-section">
                <div class="section-header">
                    📊 Recent Activity
                </div>
                <div class="section-content" id="recentActivityContent">
                    <div class="no-data">Loading...</div>
                </div>
            </div>
        </div>
        
        <div class="dashboard-grid">
            <div class="dashboard-section">
                <div class="section-header">
                    🌐 Top IP Addresses
                </div>
                <div class="section-content" id="topIPsContent">
                    <div class="no-data">Loading...</div>
                </div>
            </div>

            <div class="dashboard-section">
                <div class="section-header">
                    🎬 Video Statistics
                </div>
                <div class="section-content" id="videoStatsContent">
                    <div class="no-data">Loading...</div>
                </div>
            </div>
        </div>

        <div class="dashboard-grid">
            <div class="dashboard-section">
                <div class="section-header">
                    🌍 Geographic Distribution
                </div>
                <div class="section-content" id="geoStatsContent">
                    <div class="no-data">Loading...</div>
                </div>
            </div>

            <div class="dashboard-section">
                <div class="section-header">
                    💻 Device & Browser Stats
                </div>
                <div class="section-content" id="deviceStatsContent">
                    <div class="no-data">Loading...</div>
                </div>
            </div>
        </div>

        <div class="dashboard-section">
            <div class="section-header">
                📊 Hourly Activity Chart
            </div>
            <div class="section-content" id="hourlyStatsContent">
                <div class="no-data">Loading...</div>
            </div>
        </div>
    </div>
    
    <div class="refresh-indicator" id="refreshIndicator">
        📊 Data refreshed
    </div>
    
    <script>
        let refreshInterval;
        
        // Format timestamp to readable time
        function formatTime(timestamp) {
            const date = new Date(timestamp * 1000);
            return date.toLocaleTimeString();
        }
        
        // Format relative time
        function formatRelativeTime(timestamp) {
            const now = Date.now() / 1000;
            const diff = now - timestamp;
            
            if (diff < 60) return 'Just now';
            if (diff < 3600) return `${Math.floor(diff / 60)}m ago`;
            if (diff < 86400) return `${Math.floor(diff / 3600)}h ago`;
            return `${Math.floor(diff / 86400)}d ago`;
        }
        
        // Load dashboard data
        async function loadDashboardData() {
            try {
                const response = await fetch('/api/dashboard/stats');
                const data = await response.json();
                
                // Update basic stats
                document.getElementById('activeSessions').textContent = data.active_sessions.length;
                document.getElementById('totalIPs').textContent = data.total_unique_ips;
                document.getElementById('totalActivities').textContent = data.total_activities;
                document.getElementById('recentActivities').textContent = data.recent_activity.length;

                // Update enhanced stats
                const topCountry = Object.keys(data.country_stats || {})[0] || 'Unknown';
                document.getElementById('topCountry').textContent = topCountry;

                const topBrowser = Object.keys(data.browser_stats || {})[0] || 'Unknown';
                document.getElementById('topBrowser').textContent = topBrowser;

                // Calculate mobile percentage
                const deviceStats = data.device_stats || {};
                const mobileCount = (deviceStats.Mobile?.count || 0) + (deviceStats.Tablet?.count || 0);
                const totalDevices = Object.values(deviceStats).reduce((sum, stat) => sum + (stat.count || 0), 0);
                const mobilePercentage = totalDevices > 0 ? Math.round((mobileCount / totalDevices) * 100) : 0;
                document.getElementById('mobileUsers').textContent = mobilePercentage + '%';

                const topVideo = Object.keys(data.video_stats || {})[0] || 'None';
                document.getElementById('topVideo').textContent = topVideo;

                // Update sections
                updateActiveSessions(data.active_sessions);
                updateRecentActivity(data.recent_activity);
                updateTopIPs(data.top_ips);
                updateVideoStats(data.video_stats);
                updateGeoStats(data.country_stats);
                updateDeviceStats(data.browser_stats, data.device_stats, data.os_stats);
                updateHourlyStats(data.hourly_stats);
                
            } catch (error) {
                console.error('Failed to load dashboard data:', error);
            }
        }
        
        // Update active sessions display
        function updateActiveSessions(sessions) {
            const container = document.getElementById('activeSessionsContent');
            
            if (sessions.length === 0) {
                container.innerHTML = '<div class="no-data">No active sessions</div>';
                return;
            }
            
            container.innerHTML = sessions.map(session => `
                <div class="session-item">
                    <div class="session-info">
                        <div class="session-video">📺 ${session.video_id}</div>
                        <div class="session-ip">IP: ${session.ip_address}</div>
                        <div class="session-time">${formatRelativeTime(session.timestamp)}</div>
                    </div>
                    <div class="status-badge status-${session.status}">
                        ${session.status}
                    </div>
                </div>
            `).join('');
        }
        
        // Update recent activity display
        function updateRecentActivity(activities) {
            const container = document.getElementById('recentActivityContent');
            
            if (activities.length === 0) {
                container.innerHTML = '<div class="no-data">No recent activity</div>';
                return;
            }
            
            container.innerHTML = activities.reverse().map(activity => `
                <div class="activity-item">
                    <div class="activity-info">
                        <div class="activity-video">🎥 ${activity.video_id}</div>
                        <div class="activity-ip">IP: ${activity.ip_address}</div>
                        <div class="activity-time">${formatTime(activity.timestamp)} - ${activity.action}</div>
                    </div>
                </div>
            `).join('');
        }
        
        // Update top IPs display with enhanced info
        function updateTopIPs(ips) {
            const container = document.getElementById('topIPsContent');

            if (ips.length === 0) {
                container.innerHTML = '<div class="no-data">No IP data</div>';
                return;
            }

            container.innerHTML = ips.map(ip => {
                const geo = ip.geo_data || {};
                const device = ip.device_info || {};
                const battery = ip.extra_data?.battery;

                return `
                    <div class="ip-item">
                        <div class="ip-info">
                            <div class="ip-address">🌐 ${ip.ip}</div>
                            <div class="ip-location">📍 ${geo.city || 'Unknown'}, ${geo.country || 'Unknown'}</div>
                            <div class="ip-device">💻 ${device.browser || 'Unknown'} on ${device.os || 'Unknown'}</div>
                            ${battery ? `<div class="ip-battery">🔋 ${battery.level}% ${battery.charging ? '⚡' : ''}</div>` : ''}
                            <div class="ip-stats">
                                <span class="ip-stat">👁️ ${ip.total_views} views</span>
                                <span class="ip-stat">🎬 ${ip.unique_videos} videos</span>
                                <span class="ip-stat">⏰ ${formatRelativeTime(ip.last_seen)}</span>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // Update video statistics
        function updateVideoStats(videoStats) {
            const container = document.getElementById('videoStatsContent');

            if (Object.keys(videoStats).length === 0) {
                container.innerHTML = '<div class="no-data">No video data</div>';
                return;
            }

            container.innerHTML = Object.entries(videoStats).map(([videoId, stats]) => `
                <div class="video-item">
                    <div class="video-info">
                        <div class="video-id">🎥 ${videoId}</div>
                        <div class="video-stats">
                            <span class="video-stat">👁️ ${stats.total_views} views</span>
                            <span class="video-stat">🌐 ${stats.unique_ips} IPs</span>
                            <span class="video-stat">🌍 ${stats.countries.join(', ')}</span>
                            <span class="video-stat">📱 ${stats.devices.join(', ')}</span>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Update geographic statistics
        function updateGeoStats(countryStats) {
            const container = document.getElementById('geoStatsContent');

            if (Object.keys(countryStats).length === 0) {
                container.innerHTML = '<div class="no-data">No geographic data</div>';
                return;
            }

            container.innerHTML = Object.entries(countryStats).map(([country, stats]) => `
                <div class="geo-item">
                    <div class="geo-info">
                        <div class="geo-country">🌍 ${country}</div>
                        <div class="geo-stats">
                            <span class="geo-stat">👥 ${stats.count} users</span>
                            <span class="geo-stat">👁️ ${stats.views} views</span>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Update device and browser statistics
        function updateDeviceStats(browserStats, deviceStats, osStats) {
            const container = document.getElementById('deviceStatsContent');

            let content = '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">';

            // Browser stats
            content += '<div><h4>🌐 Browsers</h4>';
            Object.entries(browserStats).forEach(([browser, stats]) => {
                content += `<div class="device-stat-item">${browser}: ${stats.count} users (${stats.views} views)</div>`;
            });
            content += '</div>';

            // Device stats
            content += '<div><h4>📱 Devices</h4>';
            Object.entries(deviceStats).forEach(([device, stats]) => {
                content += `<div class="device-stat-item">${device}: ${stats.count} users (${stats.views} views)</div>`;
            });
            content += '</div>';

            content += '</div>';

            // OS stats
            content += '<div style="margin-top: 15px;"><h4>💻 Operating Systems</h4>';
            Object.entries(osStats).forEach(([os, stats]) => {
                content += `<div class="device-stat-item">${os}: ${stats.count} users (${stats.views} views)</div>`;
            });
            content += '</div>';

            container.innerHTML = content;
        }

        // Update hourly statistics
        function updateHourlyStats(hourlyStats) {
            const container = document.getElementById('hourlyStatsContent');

            if (Object.keys(hourlyStats).length === 0) {
                container.innerHTML = '<div class="no-data">No hourly data</div>';
                return;
            }

            // Create a simple bar chart
            const maxViews = Math.max(...Object.values(hourlyStats));

            container.innerHTML = Object.entries(hourlyStats)
                .sort(([a], [b]) => a.localeCompare(b))
                .map(([hour, views]) => {
                    const percentage = maxViews > 0 ? (views / maxViews) * 100 : 0;
                    return `
                        <div class="hour-stat">
                            <div class="hour-label">${hour}</div>
                            <div class="hour-bar">
                                <div class="hour-fill" style="width: ${percentage}%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); height: 20px; border-radius: 3px;"></div>
                            </div>
                            <div class="hour-count">${views}</div>
                        </div>
                    `;
                }).join('');
        }
        
        // Refresh data manually
        function refreshData() {
            loadDashboardData();
            showRefreshIndicator();
        }
        
        // Show refresh indicator
        function showRefreshIndicator() {
            const indicator = document.getElementById('refreshIndicator');
            indicator.classList.add('show');
            setTimeout(() => {
                indicator.classList.remove('show');
            }, 2000);
        }
        
        // Clear logs
        async function clearLogs() {
            if (confirm('Are you sure you want to clear all logs?')) {
                try {
                    await fetch('/api/dashboard/clear_logs');
                    loadDashboardData();
                    alert('Logs cleared successfully');
                } catch (error) {
                    alert('Failed to clear logs');
                }
            }
        }
        
        // Initialize dashboard
        loadDashboardData();
        
        // Auto-refresh every 10 seconds
        refreshInterval = setInterval(loadDashboardData, 10000);
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        });
    </script>
</body>
</html>
