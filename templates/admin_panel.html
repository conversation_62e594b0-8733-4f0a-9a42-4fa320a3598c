<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Admin Panel - CDN Video Player</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        /* Sidebar Navigation */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
            color: white;
            z-index: 1000;
            transition: transform 0.3s ease;
            overflow-y: auto;
            box-shadow: 2px 0 10px rgba(0,0,0,0.3);
        }

        .sidebar.collapsed {
            transform: translateX(-280px);
        }

        .sidebar-header {
            padding: 25px 20px;
            border-bottom: 1px solid #333;
            text-align: center;
            background: rgba(79, 195, 247, 0.1);
        }

        .sidebar-header h2 {
            color: #4fc3f7;
            margin-bottom: 5px;
            font-size: 1.5rem;
        }

        .sidebar-header p {
            color: #888;
            font-size: 0.9rem;
        }

        .sidebar-menu {
            padding: 20px 0;
        }

        .menu-section {
            margin-bottom: 30px;
        }

        .menu-section-title {
            padding: 10px 25px;
            color: #666;
            font-size: 0.8rem;
            text-transform: uppercase;
            font-weight: 600;
            letter-spacing: 1px;
        }

        .menu-item {
            display: block;
            padding: 15px 25px;
            color: #ccc;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            position: relative;
        }

        .menu-item:hover, .menu-item.active {
            background: rgba(79, 195, 247, 0.1);
            color: #4fc3f7;
            border-left-color: #4fc3f7;
        }

        .menu-item i {
            width: 20px;
            margin-right: 12px;
            font-size: 1.1rem;
        }

        .menu-badge {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: #ff4757;
            color: white;
            font-size: 0.7rem;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 18px;
            text-align: center;
        }

        /* Main Content */
        .main-content {
            margin-left: 280px;
            transition: margin-left 0.3s ease;
            min-height: 100vh;
        }

        .main-content.expanded {
            margin-left: 0;
        }

        /* Top Bar */
        .top-bar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .menu-toggle {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #333;
            cursor: pointer;
            padding: 10px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }

        .menu-toggle:hover {
            background: rgba(0,0,0,0.1);
        }

        .top-bar-center {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .search-box {
            position: relative;
        }

        .search-box input {
            padding: 10px 15px 10px 40px;
            border: 1px solid #ddd;
            border-radius: 25px;
            width: 300px;
            font-size: 0.9rem;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .search-box input:focus {
            border-color: #4fc3f7;
        }

        .search-box i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }

        .top-bar-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .notification-icon {
            position: relative;
            font-size: 1.2rem;
            color: #666;
            cursor: pointer;
            padding: 10px;
            border-radius: 50%;
            transition: background 0.3s ease;
        }

        .notification-icon:hover {
            background: rgba(0,0,0,0.1);
        }

        .notification-badge {
            position: absolute;
            top: 5px;
            right: 5px;
            background: #ff4757;
            color: white;
            font-size: 0.7rem;
            padding: 2px 5px;
            border-radius: 10px;
            min-width: 16px;
            text-align: center;
        }

        .admin-info {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #666;
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 25px;
            transition: background 0.3s ease;
        }

        .admin-info:hover {
            background: rgba(0,0,0,0.05);
        }

        .admin-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.1rem;
        }

        /* Content Area */
        .content-area {
            padding: 30px;
        }

        .page-header {
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 2rem;
            color: white;
            margin-bottom: 10px;
        }

        .page-subtitle {
            color: rgba(255,255,255,0.8);
            font-size: 1.1rem;
        }

        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .stat-change {
            font-size: 0.8rem;
            padding: 3px 8px;
            border-radius: 12px;
            font-weight: 600;
        }

        .stat-change.positive {
            background: rgba(46, 213, 115, 0.1);
            color: #2ed573;
        }

        .stat-change.negative {
            background: rgba(255, 71, 87, 0.1);
            color: #ff4757;
        }

        /* Content Sections */
        .content-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .section-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .section-title {
            font-size: 1.3rem;
            color: #333;
            font-weight: 600;
        }

        .section-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 195, 247, 0.4);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #ddd;
        }

        .btn-secondary:hover {
            background: #e9ecef;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 71, 87, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #2ed573 0%, #17c0eb 100%);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(46, 213, 115, 0.4);
        }

        /* Hidden content sections */
        .content-page {
            display: none;
            animation: fadeIn 0.3s ease-in-out;
        }

        .content-page.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Form controls */
        .form-control {
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            width: 100%;
            font-size: 0.9rem;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #4fc3f7;
            box-shadow: 0 0 0 3px rgba(79, 195, 247, 0.1);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        /* Alert styles */
        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-warning {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            color: #856404;
        }

        .alert-success {
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid rgba(40, 167, 69, 0.3);
            color: #155724;
        }

        .alert-danger {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.3);
            color: #721c24;
        }

        .alert-info {
            background: rgba(23, 162, 184, 0.1);
            border: 1px solid rgba(23, 162, 184, 0.3);
            color: #0c5460;
        }

        /* Loading spinner */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4fc3f7;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Progress bars */
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        /* Table styles */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .data-table th,
        .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .data-table tr:hover {
            background: rgba(79, 195, 247, 0.05);
        }

        /* Status badges */
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-online {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }

        .status-offline {
            background: rgba(108, 117, 125, 0.1);
            color: #6c757d;
        }

        .status-warning {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
        }

        .status-error {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-280px);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .search-box input {
                width: 200px;
            }
            
            .top-bar {
                padding: 15px 20px;
            }
            
            .content-area {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h2><i class="fas fa-rocket"></i> Admin Panel</h2>
            <p>CDN Video Player</p>
        </div>
        
        <div class="sidebar-menu">
            <div class="menu-section">
                <div class="menu-section-title">Dashboard</div>
                <a href="#" class="menu-item active" data-page="overview">
                    <i class="fas fa-tachometer-alt"></i> Overview
                </a>
                <a href="#" class="menu-item" data-page="analytics">
                    <i class="fas fa-chart-line"></i> Analytics
                    <span class="menu-badge">Live</span>
                </a>
                <a href="#" class="menu-item" data-page="realtime">
                    <i class="fas fa-broadcast-tower"></i> Real-time Monitor
                </a>
            </div>
            
            <div class="menu-section">
                <div class="menu-section-title">Content Management</div>
                <a href="#" class="menu-item" data-page="videos">
                    <i class="fas fa-video"></i> Video Management
                </a>
                <a href="#" class="menu-item" data-page="upload">
                    <i class="fas fa-cloud-upload-alt"></i> Upload Center
                </a>
                <a href="#" class="menu-item" data-page="cdn">
                    <i class="fas fa-server"></i> CDN Settings
                </a>
            </div>
            
            <div class="menu-section">
                <div class="menu-section-title">User Management</div>
                <a href="#" class="menu-item" data-page="users">
                    <i class="fas fa-users"></i> User Analytics
                </a>
                <a href="#" class="menu-item" data-page="sessions">
                    <i class="fas fa-clock"></i> Active Sessions
                </a>
                <a href="#" class="menu-item" data-page="security">
                    <i class="fas fa-shield-alt"></i> Security Monitor
                </a>
            </div>
            
            <div class="menu-section">
                <div class="menu-section-title">System</div>
                <a href="#" class="menu-item" data-page="performance">
                    <i class="fas fa-microchip"></i> Performance
                </a>
                <a href="#" class="menu-item" data-page="logs">
                    <i class="fas fa-file-alt"></i> System Logs
                </a>
                <a href="#" class="menu-item" data-page="settings">
                    <i class="fas fa-cog"></i> Settings
                </a>
                <a href="#" class="menu-item" data-page="backup">
                    <i class="fas fa-database"></i> Backup & Restore
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content" id="mainContent">
        <!-- Top Bar -->
        <div class="top-bar">
            <button class="menu-toggle" id="menuToggle">
                <i class="fas fa-bars"></i>
            </button>
            
            <div class="top-bar-center">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Search videos, users, logs..." id="globalSearch">
                </div>
            </div>
            
            <div class="top-bar-right">
                <div class="notification-icon" title="Notifications">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge" id="notificationCount">3</span>
                </div>
                
                <div class="admin-info" title="Admin Profile">
                    <div class="admin-avatar">A</div>
                    <div>
                        <div style="font-weight: 600;">Admin</div>
                        <div style="font-size: 0.8rem; color: #999;">Super User</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Overview Page -->
            <div class="content-page active" id="overview">
                <div class="page-header">
                    <h1 class="page-title">📊 System Overview</h1>
                    <p class="page-subtitle">Real-time insights and system status</p>
                </div>

                <!-- Stats Grid -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-card::before" style="background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);"></div>
                        <div class="stat-header">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);">
                                <i class="fas fa-eye"></i>
                            </div>
                            <div class="stat-change positive">+12.5%</div>
                        </div>
                        <div class="stat-number" id="totalViews">-</div>
                        <div class="stat-label">Total Views</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #2ed573 0%, #17c0eb 100%);">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-change positive">+8.3%</div>
                        </div>
                        <div class="stat-number" id="uniqueUsers">-</div>
                        <div class="stat-label">Unique Users</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);">
                                <i class="fas fa-video"></i>
                            </div>
                            <div class="stat-change positive">+2</div>
                        </div>
                        <div class="stat-number" id="totalVideos">-</div>
                        <div class="stat-label">Total Videos</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #ffa726 0%, #ff9800 100%);">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-change positive">+5.7%</div>
                        </div>
                        <div class="stat-number" id="activeSessions">-</div>
                        <div class="stat-label">Active Sessions</div>
                    </div>
                </div>
            </div>

            <!-- Analytics Page -->
            <div class="content-page" id="analytics">
                <div class="page-header">
                    <h1 class="page-title">📈 Advanced Analytics</h1>
                    <p class="page-subtitle">Detailed insights and performance metrics</p>
                </div>

                <div class="content-section">
                    <div class="section-header">
                        <h3 class="section-title">Geographic Distribution</h3>
                        <div class="section-actions">
                            <button class="btn btn-secondary"><i class="fas fa-download"></i> Export</button>
                        </div>
                    </div>
                    <div id="geoAnalytics">Loading geographic data...</div>
                </div>

                <div class="content-section">
                    <div class="section-header">
                        <h3 class="section-title">Device & Browser Analytics</h3>
                    </div>
                    <div id="deviceAnalytics">Loading device data...</div>
                </div>
            </div>

            <!-- Real-time Monitor Page -->
            <div class="content-page" id="realtime">
                <div class="page-header">
                    <h1 class="page-title">📡 Real-time Monitor</h1>
                    <p class="page-subtitle">Live system monitoring and alerts</p>
                </div>

                <div class="content-section">
                    <div class="section-header">
                        <h3 class="section-title">Active Sessions</h3>
                        <div class="section-actions">
                            <button class="btn btn-primary"><i class="fas fa-sync"></i> Refresh</button>
                        </div>
                    </div>
                    <div id="activeSessions">Loading active sessions...</div>
                </div>

                <div class="content-section">
                    <div class="section-header">
                        <h3 class="section-title">Recent Activity</h3>
                    </div>
                    <div id="recentActivity">Loading recent activity...</div>
                </div>
            </div>

            <!-- Video Management Page -->
            <div class="content-page" id="videos">
                <div class="page-header">
                    <h1 class="page-title">🎬 Video Management</h1>
                    <p class="page-subtitle">Manage your video content and settings</p>
                </div>

                <div class="content-section">
                    <div class="section-header">
                        <h3 class="section-title">Video Library</h3>
                        <div class="section-actions">
                            <button class="btn btn-success"><i class="fas fa-plus"></i> Add Video</button>
                            <button class="btn btn-secondary"><i class="fas fa-sync"></i> Sync Firebase</button>
                        </div>
                    </div>
                    <div id="videoLibrary">Loading video library...</div>
                </div>
            </div>

            <!-- Upload Center Page -->
            <div class="content-page" id="upload">
                <div class="page-header">
                    <h1 class="page-title">☁️ Upload Center</h1>
                    <p class="page-subtitle">Upload and manage video content</p>
                </div>

                <div class="content-section">
                    <div class="section-header">
                        <h3 class="section-title">File Upload</h3>
                    </div>
                    <div class="upload-area">
                        <div style="text-align: center; padding: 40px; border: 2px dashed #ddd; border-radius: 10px;">
                            <i class="fas fa-cloud-upload-alt" style="font-size: 3rem; color: #ccc; margin-bottom: 20px;"></i>
                            <h3>Drag & Drop Files Here</h3>
                            <p>Or click to browse files</p>
                            <button class="btn btn-primary" style="margin-top: 20px;">
                                <i class="fas fa-folder-open"></i> Browse Files
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- CDN Settings Page -->
            <div class="content-page" id="cdn">
                <div class="page-header">
                    <h1 class="page-title">🌐 CDN Settings</h1>
                    <p class="page-subtitle">Configure CDN and streaming settings</p>
                </div>

                <div class="content-section">
                    <div class="section-header">
                        <h3 class="section-title">CDN Configuration</h3>
                        <div class="section-actions">
                            <button class="btn btn-success"><i class="fas fa-save"></i> Save Changes</button>
                        </div>
                    </div>
                    <div class="settings-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <div class="setting-item">
                            <label>JWT Token Expiry</label>
                            <select class="form-control">
                                <option value="3600">1 Hour</option>
                                <option value="7200">2 Hours</option>
                                <option value="14400">4 Hours</option>
                            </select>
                        </div>
                        <div class="setting-item">
                            <label>Max Concurrent Streams</label>
                            <input type="number" class="form-control" value="100">
                        </div>
                        <div class="setting-item">
                            <label>Enable Rate Limiting</label>
                            <input type="checkbox" checked>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Analytics Page -->
            <div class="content-page" id="users">
                <div class="page-header">
                    <h1 class="page-title">👥 User Analytics</h1>
                    <p class="page-subtitle">Detailed user behavior and statistics</p>
                </div>

                <div class="content-section">
                    <div class="section-header">
                        <h3 class="section-title">Top Users</h3>
                        <div class="section-actions">
                            <button class="btn btn-secondary"><i class="fas fa-filter"></i> Filter</button>
                        </div>
                    </div>
                    <div id="topUsers">Loading user analytics...</div>
                </div>
            </div>

            <!-- Security Monitor Page -->
            <div class="content-page" id="security">
                <div class="page-header">
                    <h1 class="page-title">🛡️ Security Monitor</h1>
                    <p class="page-subtitle">Security alerts and threat monitoring</p>
                </div>

                <div class="content-section">
                    <div class="section-header">
                        <h3 class="section-title">Security Alerts</h3>
                        <div class="section-actions">
                            <button class="btn btn-danger"><i class="fas fa-ban"></i> Block IP</button>
                        </div>
                    </div>
                    <div id="securityAlerts">
                        <div style="text-align: center; color: #666; padding: 20px;">Loading security alerts...</div>
                    </div>
                </div>
            </div>

            <!-- Performance Page -->
            <div class="content-page" id="performance">
                <div class="page-header">
                    <h1 class="page-title">⚡ Performance Monitor</h1>
                    <p class="page-subtitle">System performance and optimization</p>
                </div>

                <div class="content-section">
                    <div class="section-header">
                        <h3 class="section-title">System Metrics</h3>
                    </div>
                    <div class="metrics-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;" id="systemMetrics">
                        <div class="metric-card" style="background: #f8f9fa; padding: 20px; border-radius: 10px; text-align: center;">
                            <h4><i class="fas fa-microchip"></i> CPU Usage</h4>
                            <div id="cpuUsage" style="font-size: 2rem; color: #28a745;">Loading...</div>
                            <div class="progress-bar">
                                <div class="progress-fill" id="cpuProgress" style="width: 0%;"></div>
                            </div>
                        </div>
                        <div class="metric-card" style="background: #f8f9fa; padding: 20px; border-radius: 10px; text-align: center;">
                            <h4><i class="fas fa-memory"></i> Memory Usage</h4>
                            <div id="memoryUsage" style="font-size: 2rem; color: #ffc107;">Loading...</div>
                            <div class="progress-bar">
                                <div class="progress-fill" id="memoryProgress" style="width: 0%;"></div>
                            </div>
                            <div style="font-size: 0.8rem; color: #666; margin-top: 5px;" id="memoryDetails">-</div>
                        </div>
                        <div class="metric-card" style="background: #f8f9fa; padding: 20px; border-radius: 10px; text-align: center;">
                            <h4><i class="fas fa-hdd"></i> Disk Usage</h4>
                            <div id="diskUsage" style="font-size: 2rem; color: #dc3545;">Loading...</div>
                            <div class="progress-bar">
                                <div class="progress-fill" id="diskProgress" style="width: 0%;"></div>
                            </div>
                            <div style="font-size: 0.8rem; color: #666; margin-top: 5px;" id="diskDetails">-</div>
                        </div>
                        <div class="metric-card" style="background: #f8f9fa; padding: 20px; border-radius: 10px; text-align: center;">
                            <h4><i class="fas fa-server"></i> System Info</h4>
                            <div id="systemUptime" style="font-size: 1.2rem; color: #17a2b8;">Loading...</div>
                            <div style="font-size: 0.8rem; color: #666; margin-top: 5px;" id="systemDetails">-</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Logs Page -->
            <div class="content-page" id="logs">
                <div class="page-header">
                    <h1 class="page-title">📋 System Logs</h1>
                    <p class="page-subtitle">View and analyze system logs</p>
                </div>

                <div class="content-section">
                    <div class="section-header">
                        <h3 class="section-title">Recent Logs</h3>
                        <div class="section-actions">
                            <button class="btn btn-secondary"><i class="fas fa-download"></i> Download</button>
                            <button class="btn btn-danger"><i class="fas fa-trash"></i> Clear Logs</button>
                        </div>
                    </div>
                    <div class="log-viewer" id="logViewer" style="background: #1a1a1a; color: #00ff00; padding: 20px; border-radius: 10px; font-family: monospace; height: 400px; overflow-y: auto;">
                        <div style="color: #888;">Loading system logs...</div>
                    </div>
                </div>
            </div>

            <!-- Settings Page -->
            <div class="content-page" id="settings">
                <div class="page-header">
                    <h1 class="page-title">⚙️ System Settings</h1>
                    <p class="page-subtitle">Configure system preferences and options</p>
                </div>

                <div class="content-section">
                    <div class="section-header">
                        <h3 class="section-title">General Settings</h3>
                        <div class="section-actions">
                            <button class="btn btn-success" onclick="saveSettings()"><i class="fas fa-save"></i> Save All</button>
                        </div>
                    </div>
                    <div class="settings-form">
                        <div class="form-group" style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">Dashboard Password</label>
                            <input type="password" class="form-control" value="1234567a" style="padding: 10px; border: 1px solid #ddd; border-radius: 5px; width: 100%;">
                        </div>
                        <div class="form-group" style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">Auto-refresh Interval (seconds)</label>
                            <input type="number" class="form-control" value="10" style="padding: 10px; border: 1px solid #ddd; border-radius: 5px; width: 100%;">
                        </div>
                        <div class="form-group" style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">Enable Analytics</label>
                            <input type="checkbox" checked> Collect user analytics data
                        </div>
                    </div>
                </div>
            </div>

            <!-- Backup & Restore Page -->
            <div class="content-page" id="backup">
                <div class="page-header">
                    <h1 class="page-title">💾 Backup & Restore</h1>
                    <p class="page-subtitle">Manage system backups and data recovery</p>
                </div>

                <div class="content-section">
                    <div class="section-header">
                        <h3 class="section-title">Backup Management</h3>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <div class="backup-card" style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
                            <h4><i class="fas fa-database"></i> Create Backup</h4>
                            <p>Create a full system backup including videos and analytics data.</p>
                            <button class="btn btn-primary" onclick="createBackup()"><i class="fas fa-save"></i> Create Backup</button>
                            <div id="backupStatus" style="margin-top: 10px;"></div>
                        </div>
                        <div class="backup-card" style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
                            <h4><i class="fas fa-upload"></i> Restore Backup</h4>
                            <p>Restore system from a previous backup file.</p>
                            <button class="btn btn-secondary"><i class="fas fa-upload"></i> Upload Backup</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Global variables
        let currentPage = 'overview';
        let refreshInterval;
        
        // Initialize admin panel
        document.addEventListener('DOMContentLoaded', function() {
            initializeAdminPanel();
            loadDashboardData();
            startAutoRefresh();
        });

        function initializeAdminPanel() {
            // Menu toggle functionality
            const menuToggle = document.getElementById('menuToggle');
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');

            menuToggle.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            });

            // Menu item navigation
            const menuItems = document.querySelectorAll('.menu-item');
            menuItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Remove active class from all items
                    menuItems.forEach(mi => mi.classList.remove('active'));
                    
                    // Add active class to clicked item
                    this.classList.add('active');
                    
                    // Get page to show
                    const page = this.getAttribute('data-page');
                    showPage(page);
                });
            });

            // Global search functionality
            const globalSearch = document.getElementById('globalSearch');
            globalSearch.addEventListener('input', function() {
                const query = this.value.toLowerCase();
                performGlobalSearch(query);
            });
        }

        function showPage(pageId) {
            // Hide all pages
            const pages = document.querySelectorAll('.content-page');
            pages.forEach(page => page.classList.remove('active'));
            
            // Show selected page
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
                currentPage = pageId;
                
                // Load page-specific data
                loadPageData(pageId);
            }
        }

        function loadPageData(pageId) {
            switch(pageId) {
                case 'overview':
                    loadDashboardData();
                    break;
                case 'analytics':
                    loadAnalyticsData();
                    break;
                case 'realtime':
                    loadRealtimeData();
                    break;
                case 'videos':
                    loadVideoLibrary();
                    break;
                case 'users':
                    loadUserAnalytics();
                    break;
                case 'security':
                    loadSecurityData();
                    break;
                case 'performance':
                    loadPerformanceData();
                    break;
                case 'logs':
                    loadSystemLogs();
                    break;
                case 'security':
                    loadSecurityData();
                    break;
            }
        }

        async function loadAnalyticsData() {
            try {
                const response = await fetch('/api/dashboard/stats');
                if (response.ok) {
                    const data = await response.json();
                    updateAnalyticsPage(data);
                }
            } catch (error) {
                console.error('Failed to load analytics data:', error);
            }
        }

        function updateAnalyticsPage(data) {
            // Update geographic analytics
            const geoElement = document.getElementById('geoAnalytics');
            if (geoElement && data.country_stats) {
                let geoHtml = '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">';
                Object.entries(data.country_stats).forEach(([country, stats]) => {
                    geoHtml += `
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                            <h4>${country}</h4>
                            <div style="font-size: 1.5rem; color: #4fc3f7;">${stats.count}</div>
                            <div style="color: #666; font-size: 0.9rem;">${stats.views} views</div>
                        </div>
                    `;
                });
                geoHtml += '</div>';
                geoElement.innerHTML = geoHtml;
            }

            // Update device analytics
            const deviceElement = document.getElementById('deviceAnalytics');
            if (deviceElement && data.browser_stats) {
                let deviceHtml = '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">';

                // Browser stats
                deviceHtml += '<div><h4>🌐 Browsers</h4>';
                Object.entries(data.browser_stats).forEach(([browser, stats]) => {
                    deviceHtml += `
                        <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #eee;">
                            <span>${browser}</span>
                            <span style="color: #4fc3f7; font-weight: 600;">${stats.count} users</span>
                        </div>
                    `;
                });
                deviceHtml += '</div>';

                // Device stats
                if (data.device_stats) {
                    deviceHtml += '<div><h4>📱 Devices</h4>';
                    Object.entries(data.device_stats).forEach(([device, stats]) => {
                        deviceHtml += `
                            <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #eee;">
                                <span>${device}</span>
                                <span style="color: #2ed573; font-weight: 600;">${stats.count} users</span>
                            </div>
                        `;
                    });
                    deviceHtml += '</div>';
                }

                deviceHtml += '</div>';
                deviceElement.innerHTML = deviceHtml;
            }
        }

        async function loadRealtimeData() {
            try {
                const response = await fetch('/api/dashboard/stats');
                if (response.ok) {
                    const data = await response.json();
                    updateRealtimePage(data);
                }
            } catch (error) {
                console.error('Failed to load realtime data:', error);
            }
        }

        function updateRealtimePage(data) {
            // Update active sessions
            const sessionsElement = document.getElementById('activeSessions');
            if (sessionsElement && data.active_sessions) {
                let sessionsHtml = '';
                if (data.active_sessions.length === 0) {
                    sessionsHtml = '<div style="text-align: center; color: #666; padding: 20px;">No active sessions</div>';
                } else {
                    sessionsHtml = '<div style="display: grid; gap: 10px;">';
                    data.active_sessions.forEach(session => {
                        sessionsHtml += `
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <strong>${session.video_id}</strong>
                                    <div style="color: #666; font-size: 0.9rem;">${session.ip_address}</div>
                                </div>
                                <div style="color: #28a745; font-weight: 600;">${session.status}</div>
                            </div>
                        `;
                    });
                    sessionsHtml += '</div>';
                }
                sessionsElement.innerHTML = sessionsHtml;
            }

            // Update recent activity
            const activityElement = document.getElementById('recentActivity');
            if (activityElement && data.recent_activity) {
                let activityHtml = '<div style="max-height: 400px; overflow-y: auto;">';
                data.recent_activity.slice(0, 20).forEach(activity => {
                    const timeAgo = formatTimeAgo(activity.timestamp);
                    activityHtml += `
                        <div style="padding: 10px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;">
                            <div>
                                <strong>${activity.action}</strong> on ${activity.video_id}
                                <div style="color: #666; font-size: 0.9rem;">${activity.ip_address}</div>
                            </div>
                            <div style="color: #999; font-size: 0.8rem;">${timeAgo}</div>
                        </div>
                    `;
                });
                activityHtml += '</div>';
                activityElement.innerHTML = activityHtml;
            }
        }

        async function loadVideoLibrary() {
            try {
                const response = await fetch('/api/videos');
                if (response.ok) {
                    const videos = await response.json();
                    updateVideoLibrary(videos);
                }
            } catch (error) {
                console.error('Failed to load video library:', error);
                document.getElementById('videoLibrary').innerHTML = '<div style="color: #dc3545;">Failed to load video library</div>';
            }
        }

        function updateVideoLibrary(videos) {
            const libraryElement = document.getElementById('videoLibrary');
            if (libraryElement) {
                let libraryHtml = '<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px;">';

                if (videos.length === 0) {
                    libraryHtml = '<div style="text-align: center; color: #666; padding: 40px;">No videos found</div>';
                } else {
                    videos.forEach(video => {
                        libraryHtml += `
                            <div style="background: #f8f9fa; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                <div style="padding: 20px;">
                                    <h4 style="margin-bottom: 10px;">${video.title || video.id}</h4>
                                    <div style="color: #666; margin-bottom: 15px;">
                                        <div>📹 ID: ${video.id}</div>
                                        <div>👁️ Views: ${video.views || 0}</div>
                                        <div>📊 Type: ${video.type || 'Unknown'}</div>
                                    </div>
                                    <div style="display: flex; gap: 10px;">
                                        <button class="btn btn-primary" onclick="playVideo('${video.id}')">
                                            <i class="fas fa-play"></i> Play
                                        </button>
                                        <button class="btn btn-secondary" onclick="editVideo('${video.id}')">
                                            <i class="fas fa-edit"></i> Edit
                                        </button>
                                        <button class="btn btn-danger" onclick="deleteVideo('${video.id}')">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                }

                libraryHtml += '</div>';
                libraryElement.innerHTML = libraryHtml;
            }
        }

        async function loadUserAnalytics() {
            try {
                const response = await fetch('/api/dashboard/stats');
                if (response.ok) {
                    const data = await response.json();
                    updateUserAnalytics(data);
                }
            } catch (error) {
                console.error('Failed to load user analytics:', error);
            }
        }

        function updateUserAnalytics(data) {
            const usersElement = document.getElementById('topUsers');
            if (usersElement && data.top_ips) {
                let usersHtml = '<div style="display: grid; gap: 15px;">';
                data.top_ips.slice(0, 10).forEach((user, index) => {
                    const geo = user.geo_data || {};
                    const device = user.device_info || {};
                    usersHtml += `
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; display: flex; justify-content: space-between; align-items: center;">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%); color: white; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold;">
                                    ${index + 1}
                                </div>
                                <div>
                                    <strong>${user.ip}</strong>
                                    <div style="color: #666; font-size: 0.9rem;">
                                        📍 ${geo.city || 'Unknown'}, ${geo.country || 'Unknown'}
                                    </div>
                                    <div style="color: #666; font-size: 0.9rem;">
                                        💻 ${device.browser || 'Unknown'} on ${device.os || 'Unknown'}
                                    </div>
                                </div>
                            </div>
                            <div style="text-align: right;">
                                <div style="font-size: 1.2rem; font-weight: 600; color: #4fc3f7;">${user.total_views}</div>
                                <div style="color: #666; font-size: 0.9rem;">views</div>
                            </div>
                        </div>
                    `;
                });
                usersHtml += '</div>';
                usersElement.innerHTML = usersHtml;
            }
        }

        // Utility functions
        function formatTimeAgo(timestamp) {
            const now = Date.now() / 1000;
            const diff = now - timestamp;

            if (diff < 60) return 'Just now';
            if (diff < 3600) return Math.floor(diff / 60) + 'm ago';
            if (diff < 86400) return Math.floor(diff / 3600) + 'h ago';
            return Math.floor(diff / 86400) + 'd ago';
        }

        // Video management functions
        function playVideo(videoId) {
            window.open(`/video-${videoId}`, '_blank');
        }

        function editVideo(videoId) {
            alert(`Edit video: ${videoId}`);
        }

        function deleteVideo(videoId) {
            if (confirm(`Are you sure you want to delete video ${videoId}?`)) {
                // Implement delete functionality
                alert(`Delete video: ${videoId}`);
            }
        }

        // Load placeholder data for other sections
        function loadSecurityData() {
            // Placeholder for security data
        }

        function loadPerformanceData() {
            // Placeholder for performance data
        }

        function loadSystemLogs() {
            // Placeholder for system logs
        }

        async function loadDashboardData() {
            try {
                const response = await fetch('/api/dashboard/stats');
                if (response.ok) {
                    const data = await response.json();
                    updateOverviewStats(data);
                }
            } catch (error) {
                console.error('Failed to load dashboard data:', error);
            }
        }

        function updateOverviewStats(data) {
            // Update stat cards
            document.getElementById('totalViews').textContent = data.total_activities || 0;
            document.getElementById('uniqueUsers').textContent = data.total_unique_ips || 0;
            document.getElementById('totalVideos').textContent = Object.keys(data.video_stats || {}).length;
            document.getElementById('activeSessions').textContent = data.active_sessions?.length || 0;
        }

        function startAutoRefresh() {
            refreshInterval = setInterval(() => {
                if (currentPage === 'overview') {
                    loadDashboardData();
                } else if (currentPage === 'realtime') {
                    loadRealtimeData();
                } else if (currentPage === 'analytics') {
                    loadAnalyticsData();
                }

                // Update notification count
                updateNotificationCount();
            }, 10000); // Refresh every 10 seconds
        }

        async function updateNotificationCount() {
            try {
                const response = await fetch('/api/admin/notifications');
                if (response.ok) {
                    const notifications = await response.json();
                    const unreadCount = notifications.filter(n => !n.read).length;
                    document.getElementById('notificationCount').textContent = unreadCount;
                }
            } catch (error) {
                console.error('Failed to update notification count:', error);
            }
        }

        // Enhanced error handling
        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'alert alert-danger';
            errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;

            // Insert at top of content area
            const contentArea = document.querySelector('.content-area');
            contentArea.insertBefore(errorDiv, contentArea.firstChild);

            // Auto remove after 5 seconds
            setTimeout(() => {
                errorDiv.remove();
            }, 5000);
        }

        function showSuccess(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'alert alert-success';
            successDiv.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;

            // Insert at top of content area
            const contentArea = document.querySelector('.content-area');
            contentArea.insertBefore(successDiv, contentArea.firstChild);

            // Auto remove after 3 seconds
            setTimeout(() => {
                successDiv.remove();
            }, 3000);
        }

        // Loading state management
        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.innerHTML = '<div style="text-align: center; padding: 20px;"><div class="loading-spinner"></div> Loading...</div>';
            }
        }

        // Enhanced video management functions
        async function deleteVideo(videoId) {
            if (!confirm(`Are you sure you want to delete video ${videoId}? This action cannot be undone.`)) {
                return;
            }

            try {
                const response = await fetch(`/api/admin/video/${videoId}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    const result = await response.json();
                    showSuccess(result.message);
                    loadVideoLibrary(); // Refresh the video library
                } else {
                    const error = await response.json();
                    showError(error.error || 'Failed to delete video');
                }
            } catch (error) {
                showError('Network error: ' + error.message);
            }
        }

        // System control functions
        async function createBackup() {
            try {
                showLoading('backupStatus');

                const response = await fetch('/api/admin/backup', {
                    method: 'POST'
                });

                if (response.ok) {
                    const result = await response.json();
                    showSuccess(result.message);
                } else {
                    const error = await response.json();
                    showError(error.error || 'Failed to create backup');
                }
            } catch (error) {
                showError('Network error: ' + error.message);
            }
        }

        async function saveSettings() {
            try {
                const settings = {
                    dashboard_password: document.querySelector('input[type="password"]').value,
                    auto_refresh_interval: parseInt(document.querySelector('input[type="number"]').value),
                    enable_analytics: document.querySelector('input[type="checkbox"]').checked
                };

                const response = await fetch('/api/admin/settings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(settings)
                });

                if (response.ok) {
                    const result = await response.json();
                    showSuccess(result.message);
                } else {
                    const error = await response.json();
                    showError(error.error || 'Failed to save settings');
                }
            } catch (error) {
                showError('Network error: ' + error.message);
            }
        }

        // Real-time updates for performance metrics
        async function loadPerformanceData() {
            try {
                const response = await fetch('/api/admin/system_info');
                if (response.ok) {
                    const data = await response.json();
                    updatePerformanceMetrics(data);
                } else {
                    const error = await response.json();
                    showError('Performance data: ' + error.error);
                }
            } catch (error) {
                console.error('Failed to load performance data:', error);
                showError('Failed to load performance data');
            }
        }

        function updatePerformanceMetrics(data) {
            // Update CPU usage
            const cpuElement = document.getElementById('cpuUsage');
            const cpuProgress = document.getElementById('cpuProgress');
            if (cpuElement && data.cpu_percent !== undefined) {
                cpuElement.textContent = data.cpu_percent + '%';
                cpuElement.style.color = data.cpu_percent > 80 ? '#dc3545' : data.cpu_percent > 60 ? '#ffc107' : '#28a745';
                if (cpuProgress) cpuProgress.style.width = data.cpu_percent + '%';
            }

            // Update Memory usage
            const memoryElement = document.getElementById('memoryUsage');
            const memoryProgress = document.getElementById('memoryProgress');
            const memoryDetails = document.getElementById('memoryDetails');
            if (memoryElement && data.memory_percent !== undefined) {
                memoryElement.textContent = data.memory_percent + '%';
                memoryElement.style.color = data.memory_percent > 85 ? '#dc3545' : data.memory_percent > 70 ? '#ffc107' : '#28a745';
                if (memoryProgress) memoryProgress.style.width = data.memory_percent + '%';
                if (memoryDetails && data.memory_used && data.memory_total) {
                    memoryDetails.textContent = `${data.memory_used}GB / ${data.memory_total}GB`;
                }
            }

            // Update Disk usage
            const diskElement = document.getElementById('diskUsage');
            const diskProgress = document.getElementById('diskProgress');
            const diskDetails = document.getElementById('diskDetails');
            if (diskElement && data.disk_percent !== undefined) {
                diskElement.textContent = data.disk_percent + '%';
                diskElement.style.color = data.disk_percent > 90 ? '#dc3545' : data.disk_percent > 75 ? '#ffc107' : '#28a745';
                if (diskProgress) diskProgress.style.width = data.disk_percent + '%';
                if (diskDetails && data.disk_used && data.disk_total) {
                    diskDetails.textContent = `${data.disk_used}GB / ${data.disk_total}GB`;
                }
            }

            // Update System info
            const uptimeElement = document.getElementById('systemUptime');
            const systemDetails = document.getElementById('systemDetails');
            if (uptimeElement && data.uptime_seconds !== undefined) {
                const days = Math.floor(data.uptime_seconds / 86400);
                const hours = Math.floor((data.uptime_seconds % 86400) / 3600);
                const minutes = Math.floor((data.uptime_seconds % 3600) / 60);
                uptimeElement.textContent = `${days}d ${hours}h ${minutes}m`;

                if (systemDetails) {
                    systemDetails.textContent = `${data.cpu_count || 'Unknown'} CPU cores, ${data.process_count || 'Unknown'} processes`;
                }
            }
        }

        // Load system logs
        async function loadSystemLogs() {
            try {
                const response = await fetch('/api/admin/logs');
                if (response.ok) {
                    const logs = await response.json();
                    updateSystemLogs(logs);
                } else {
                    const error = await response.json();
                    showError('System logs: ' + error.error);
                }
            } catch (error) {
                console.error('Failed to load system logs:', error);
                showError('Failed to load system logs');
            }
        }

        function updateSystemLogs(logs) {
            const logViewer = document.getElementById('logViewer');
            if (logViewer) {
                let logHtml = '';
                logs.forEach(log => {
                    const timestamp = new Date(log.timestamp * 1000).toLocaleString();
                    const levelColor = {
                        'INFO': '#00ff00',
                        'WARNING': '#ffff00',
                        'ERROR': '#ff0000',
                        'DEBUG': '#00ffff'
                    }[log.level] || '#ffffff';

                    logHtml += `<div style="color: ${levelColor};">[${timestamp}] ${log.level}: ${log.message}</div>`;
                });

                if (logHtml === '') {
                    logHtml = '<div style="color: #888;">No logs available</div>';
                }

                logViewer.innerHTML = logHtml;
                logViewer.scrollTop = logViewer.scrollHeight; // Scroll to bottom
            }
        }

        // Load security alerts
        async function loadSecurityData() {
            try {
                const response = await fetch('/api/admin/notifications');
                if (response.ok) {
                    const notifications = await response.json();
                    updateSecurityAlerts(notifications);
                }
            } catch (error) {
                console.error('Failed to load security data:', error);
            }
        }

        function updateSecurityAlerts(notifications) {
            const alertsElement = document.getElementById('securityAlerts');
            if (alertsElement) {
                let alertsHtml = '';

                const securityAlerts = notifications.filter(n =>
                    n.type === 'warning' || n.type === 'error'
                );

                if (securityAlerts.length === 0) {
                    alertsHtml = '<div style="text-align: center; color: #28a745; padding: 20px;"><i class="fas fa-shield-alt"></i> No security alerts</div>';
                } else {
                    securityAlerts.forEach(alert => {
                        const alertClass = alert.type === 'error' ? 'alert-danger' : 'alert-warning';
                        const icon = alert.type === 'error' ? 'fas fa-exclamation-circle' : 'fas fa-exclamation-triangle';
                        alertsHtml += `
                            <div class="alert ${alertClass}">
                                <i class="${icon}"></i>
                                <strong>${alert.title}:</strong> ${alert.message}
                            </div>
                        `;
                    });
                }

                alertsElement.innerHTML = alertsHtml;
            }
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + R for refresh
            if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
                e.preventDefault();
                loadPageData(currentPage);
                showSuccess('Page refreshed');
            }

            // Escape to close modals/alerts
            if (e.key === 'Escape') {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(alert => alert.remove());
            }
        });

        function performGlobalSearch(query) {
            // Implement global search functionality
            console.log('Searching for:', query);
        }

        // Responsive sidebar for mobile
        if (window.innerWidth <= 768) {
            document.getElementById('sidebar').classList.add('collapsed');
            document.getElementById('mainContent').classList.add('expanded');
        }
    </script>
</body>
</html>
