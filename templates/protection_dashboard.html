
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anti-Scraping Dashboard</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            margin: 0;
            padding: 20px;
        }
        .dashboard {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #ffd700;
            margin-bottom: 10px;
        }
        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }
        .activities {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }
        .activity-item {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid #ff6b6b;
        }
        .activity-time {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        .refresh-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            margin: 20px auto;
            display: block;
        }
        .alert {
            background: #ff4757;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .success {
            background: #2ed573;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>🛡️ Anti-Scraping Protection Dashboard</h1>
            <p>Real-time monitoring and protection statistics</p>
        </div>
        
        <div id="alerts"></div>
        
        <div class="stats-grid" id="statsGrid">
            <!-- Stats will be loaded here -->
        </div>
        
        <div class="activities">
            <h3>🚨 Recent Suspicious Activities</h3>
            <div id="activitiesList">
                <!-- Activities will be loaded here -->
            </div>
        </div>
        
        <button class="refresh-btn" onclick="loadDashboard()">🔄 Refresh Data</button>
    </div>

    <script>
        async function loadDashboard() {
            try {
                const response = await fetch('/api/protection_report');
                const data = await response.json();
                
                updateStats(data.stats, data.protection_effectiveness);
                updateActivities(data.recent_activities);
                updateAlerts(data);
                
            } catch (error) {
                console.error('Failed to load dashboard:', error);
            }
        }
        
        function updateStats(stats, effectiveness) {
            const statsGrid = document.getElementById('statsGrid');
            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${stats.total_requests}</div>
                    <div class="stat-label">Total Requests</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.blocked_requests}</div>
                    <div class="stat-label">Blocked Requests</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.devtools_detections}</div>
                    <div class="stat-label">DevTools Detections</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.automation_detections}</div>
                    <div class="stat-label">Automation Blocked</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${effectiveness.block_rate.toFixed(1)}%</div>
                    <div class="stat-label">Block Rate</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${effectiveness.detection_rate.toFixed(1)}%</div>
                    <div class="stat-label">Detection Rate</div>
                </div>
            `;
        }
        
        function updateActivities(activities) {
            const activitiesList = document.getElementById('activitiesList');
            
            if (activities.length === 0) {
                activitiesList.innerHTML = '<p>No suspicious activities detected.</p>';
                return;
            }
            
            activitiesList.innerHTML = activities.map(activity => `
                <div class="activity-item">
                    <strong>${activity.type.replace('_', ' ').toUpperCase()}</strong><br>
                    IP: ${activity.ip}<br>
                    ${activity.video_id ? `Video: ${activity.video_id}<br>` : ''}
                    <div class="activity-time">${new Date(activity.timestamp * 1000).toLocaleString()}</div>
                </div>
            `).join('');
        }
        
        function updateAlerts(data) {
            const alerts = document.getElementById('alerts');
            let alertsHtml = '';
            
            if (data.blocked_ips.length > 0) {
                alertsHtml += `
                    <div class="alert">
                        🚫 ${data.blocked_ips.length} IP(s) currently blocked
                    </div>
                `;
            }
            
            if (data.protection_effectiveness.block_rate > 10) {
                alertsHtml += `
                    <div class="alert">
                        ⚠️ High block rate detected (${data.protection_effectiveness.block_rate.toFixed(1)}%)
                    </div>
                `;
            } else {
                alertsHtml += `
                    <div class="success">
                        ✅ Protection systems operating normally
                    </div>
                `;
            }
            
            alerts.innerHTML = alertsHtml;
        }
        
        // Auto-refresh every 30 seconds
        setInterval(loadDashboard, 30000);
        
        // Load on page load
        loadDashboard();
    </script>
</body>
</html>
    