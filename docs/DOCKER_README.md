# 🐳 Docker Deployment Guide

## Multi-User CDN Video Player - Cross-Platform Docker Edition

This guide covers deploying the CDN Video Player using Docker with cross-platform support (AMD64, ARM64, ARM/v7) and optimizations for low CPU and RAM usage while supporting multiple concurrent users.

## 🚀 Quick Start

### Prerequisites
- Docker 20.10+ (any platform)
- Docker Compose 1.29+ or Docker Compose V2
- Python 3.8+ compatible base image
- 512MB+ RAM available
- 1GB+ disk space
- Cross-platform support: AMD64, ARM64, ARM/v7

### 1. <PERSON><PERSON> and Setup
```bash
git clone <your-repo>
cd player
cp .env.production .env
# Edit .env with your Firebase and Google Drive credentials
```

### 2. Deploy
```bash
./deploy.sh deploy
```

### 3. Access
- **Application**: http://localhost:5000
- **API Stats**: http://localhost:5000/api/stats

## 📋 Available Commands

```bash
./deploy.sh build     # Build Docker image
./deploy.sh deploy    # Build and deploy
./deploy.sh start     # Start containers
./deploy.sh stop      # Stop containers
./deploy.sh restart   # Restart containers
./deploy.sh logs      # View logs
./deploy.sh status    # Check status and resources
./deploy.sh cleanup   # Remove everything
```

## 🏗️ Multi-Architecture Builds

### Build for Multiple Platforms
```bash
# Build for local platform only
./build-multiarch.sh latest local

# Build for AMD64, ARM64, ARM/v7
./build-multiarch.sh latest multiarch

# Build and push to registry
./build-multiarch.sh v1.0.0 push
```

### Supported Platforms
- **AMD64**: Intel/AMD 64-bit (x86_64)
- **ARM64**: ARM 64-bit (aarch64) - Apple M1/M2, AWS Graviton
- **ARM/v7**: ARM 32-bit - Raspberry Pi 3/4, embedded devices

### Platform Detection
The Docker image automatically detects and optimizes for:
- CPU architecture (x86_64, aarch64, armv7l)
- Available CPU cores (1-4 workers)
- Memory constraints (auto-scaling)
- Python version (3.8+ compatible)

## 🔧 Configuration

### Environment Variables (.env)
```bash
# Required
FIREBASE_DATABASE_URL=https://your-project.firebaseio.com/
FIREBASE_API_KEY=your_firebase_key
DRIVE_API=your_google_drive_key

# Optional Performance Tuning
MAX_CONCURRENT_USERS=100
SESSION_TIMEOUT=1800
MEMORY_CLEANUP_INTERVAL=300
RATE_LIMIT_PER_MINUTE=60
```

### Resource Limits
Default Docker Compose limits:
- **Memory**: 256MB limit, 128MB reserved
- **CPU**: 0.5 cores limit, 0.25 cores reserved

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx Proxy   │    │  CDN Video App  │    │   Firebase DB   │
│   (Optional)    │───▶│   (Gunicorn)    │───▶│   (External)    │
│   Port 80/443   │    │   Port 5000     │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📊 Performance Metrics

### Resource Usage (100 concurrent users)
- **RAM**: ~200MB total
- **CPU**: ~30% of 1 core
- **Network**: Depends on video streaming
- **Disk**: ~50MB for app + cache

### Optimizations Included
- ✅ Multi-stage Docker build
- ✅ Non-root user execution
- ✅ Memory-optimized Python settings
- ✅ Gunicorn with Gevent workers
- ✅ Automatic garbage collection
- ✅ LRU caching with limits
- ✅ Session cleanup automation

## 🔒 Security Features

- **Non-root container execution**
- **Bot detection and blocking**
- **Rate limiting per user**
- **Security headers**
- **Input validation**
- **Token-based video access**

## 🌐 Production Deployment

### With Nginx Reverse Proxy
1. Uncomment nginx service in `docker-compose.yml`
2. Configure SSL certificates in `./ssl/`
3. Update domain in `nginx.conf`
4. Deploy: `./deploy.sh deploy`

### Environment-specific Configs
```bash
# Development
docker-compose -f docker-compose.yml up

# Production with Nginx
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up
```

## 📈 Monitoring

### Health Checks
- **Container**: Built-in Docker health check
- **Application**: `/api/stats` endpoint
- **Nginx**: `/health` endpoint

### Logs
```bash
# Application logs
./deploy.sh logs

# Specific service logs
docker-compose logs cdn-video-player

# Follow logs in real-time
docker-compose logs -f
```

### Resource Monitoring
```bash
# Current usage
./deploy.sh status

# Continuous monitoring
docker stats cdn_video_player
```

## 🔧 Troubleshooting

### Common Issues

**Container won't start:**
```bash
# Check logs
./deploy.sh logs

# Check configuration
docker-compose config
```

**High memory usage:**
```bash
# Check current usage
docker stats --no-stream

# Restart to clear memory
./deploy.sh restart
```

**Permission errors:**
```bash
# Fix file permissions
sudo chown -R $USER:$USER .
chmod +x deploy.sh
```

### Debug Mode
```bash
# Run with debug output
docker-compose up --build

# Access container shell
docker exec -it cdn_video_player bash
```

## 🔄 Updates

### Update Application
```bash
git pull
./deploy.sh deploy
```

### Update Dependencies
```bash
# Rebuild without cache
docker-compose build --no-cache
./deploy.sh restart
```

## 📝 Customization

### Custom Gunicorn Config
Edit `gunicorn_config.py` for:
- Worker count adjustment
- Timeout settings
- Memory limits

### Custom Nginx Config
Edit `nginx.conf` for:
- SSL configuration
- Rate limiting
- Caching rules

### Resource Limits
Edit `docker-compose.yml` deploy section:
```yaml
deploy:
  resources:
    limits:
      memory: 512M  # Increase if needed
      cpus: '1.0'   # Increase if needed
```

## 🆘 Support

For issues:
1. Check logs: `./deploy.sh logs`
2. Check status: `./deploy.sh status`
3. Restart services: `./deploy.sh restart`
4. Full cleanup: `./deploy.sh cleanup`
