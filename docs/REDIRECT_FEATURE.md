# 🔗 First Play Redirect Feature

## 🎯 Overview

The CDN Video Player now includes a **first play redirect** feature that opens a specified URL in a new tab when the user first interacts with the video player.

## ⚙️ Configuration

### Environment Variables

Add these to your `.env` file:

```bash
# Redirect Settings
REDIR=True
REDIR_LINK=https://example.com/your-redirect-url
```

### Settings Explanation

- **`REDIR`**: Enable/disable redirect functionality
  - `True` = Redirect enabled
  - `False` = Redirect disabled
  
- **`REDIR_LINK`**: The URL to redirect to on first play
  - Must be a valid HTTP/HTTPS URL
  - Opens in a new tab/window

## 🎮 How It Works

1. **First Interaction**: When user clicks play or taps the video for the first time
2. **Redirect Trigger**: Opens `REDIR_LINK` in a new tab
3. **One-Time Only**: Subsequent plays won't trigger redirect
4. **Session Based**: Resets when page is refreshed

## 🔧 Implementation Details

### JavaScript Events
- Listens for `play` event
- Listens for `click` event (when video is paused)
- Tracks redirect state with `hasRedirected` flag

### Security Features
- Opens in new tab (`_blank`)
- Doesn't interrupt video playback
- No redirect loops or spam

## 📝 Example Usage

### Marketing Campaign
```bash
REDIR=True
REDIR_LINK=https://your-website.com/special-offer
```

### Social Media
```bash
REDIR=True
REDIR_LINK=https://twitter.com/your-account
```

### Subscription Page
```bash
REDIR=True
REDIR_LINK=https://your-site.com/subscribe
```

## 🚀 Production Deployment

For production, update your hosting platform's environment variables:

```bash
REDIR=True
REDIR_LINK=https://your-production-redirect-url.com
```

## 🛡️ Security Notes

- Redirect URL is server-side controlled
- No client-side manipulation possible
- Respects user experience (new tab only)
- One redirect per session prevents spam

## 🎬 User Experience

- ✅ Non-intrusive (new tab)
- ✅ One-time only per session
- ✅ Doesn't interrupt video
- ✅ Works on mobile and desktop
- ✅ Respects user intent

Perfect for monetization, marketing campaigns, or driving traffic to your main site! 🔥
