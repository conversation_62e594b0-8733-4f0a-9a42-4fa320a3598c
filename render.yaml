# Render deployment configuration for CDN Video Player
# Optimized for Python 3.13+ compatibility

services:
  - type: web
    name: cdn-video-player
    env: python
    plan: starter  # Free tier
    buildCommand: |
      # Install system dependencies
      apt-get update && apt-get install -y build-essential
      
      # Use smart dependency installer
      chmod +x install-deps.sh
      ./install-deps.sh
      
      # Verify installation
      python3 -c "import flask, pyrebase, eventlet; print('✅ All dependencies ready')"
    
    startCommand: |
      # Load runtime configuration
      if [ -f runtime_config.env ]; then
        source runtime_config.env
      else
        export WORKER_CLASS=eventlet
      fi
      
      # Start with gunicorn
      gunicorn \
        --bind 0.0.0.0:$PORT \
        --workers 2 \
        --worker-class $WORKER_CLASS \
        --worker-connections 1000 \
        --max-requests 1000 \
        --max-requests-jitter 50 \
        --timeout 30 \
        --keepalive 2 \
        --preload \
        --access-logfile - \
        --error-logfile - \
        cdn_player:app
    
    envVars:
      - key: FLASK_ENV
        value: production
      - key: PYTHONOPTIMIZE
        value: "1"
      - key: PYTHONDONTWRITEBYTECODE
        value: "1"
      - key: PYTHONUNBUFFERED
        value: "1"
      # Add your Firebase and Google Drive credentials here
      - key: FIREBASE_DATABASE_URL
        sync: false  # Set in Render dashboard
      - key: FIREBASE_API_KEY
        sync: false  # Set in Render dashboard
      - key: DRIVE_API
        sync: false  # Set in Render dashboard
    
    healthCheckPath: /api/stats
    
    # Resource limits for free tier
    disk:
      name: cdn-storage
      mountPath: /app/cache
      sizeGB: 1
