version: '3.8'

services:
  cdn-video-player:
    build: .
    container_name: cdn_video_player
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - PYTHONOPTIMIZE=1
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
    volumes:
      - ./player_cache.json:/app/player_cache.json
      - ./logs:/app/logs
      - ./.env:/app/.env:ro
    networks:
      - cdn_network
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:5000/api/stats', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Redis for enhanced caching (uncomment if needed)
  # redis:
  #   image: redis:7-alpine
  #   container_name: cdn_redis
  #   restart: unless-stopped
  #   command: redis-server --maxmemory 64mb --maxmemory-policy allkeys-lru
  #   volumes:
  #     - redis_data:/data
  #   networks:
  #     - cdn_network
  #   deploy:
  #     resources:
  #       limits:
  #         memory: 128M
  #         cpus: '0.25'

  # Optional: Nginx reverse proxy (uncomment if needed)
  # nginx:
  #   image: nginx:alpine
  #   container_name: cdn_nginx
  #   restart: unless-stopped
  #   ports:
  #     - "80:80"
  #     - "443:443"
  #   volumes:
  #     - ./nginx.conf:/etc/nginx/nginx.conf:ro
  #     - ./ssl:/etc/nginx/ssl:ro
  #   depends_on:
  #     - cdn-video-player
  #   networks:
  #     - cdn_network
  #   deploy:
  #     resources:
  #       limits:
  #         memory: 64M
  #         cpus: '0.25'

networks:
  cdn_network:
    driver: bridge

volumes:
  redis_data:
    driver: local
