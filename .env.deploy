# CDN Video Player - Deployment Environment Variables
# Copy these to your hosting platform's environment variables section

# Firebase Configuration - sorathe-sky project
FIREBASE_API_KEY=AIzaSyDPybiL00qV5gid1oYclwFW7apLrSSiTQA
FIREBASE_AUTH_DOMAIN=sorathe-sky.firebaseapp.com
FIREBASE_DATABASE_URL=https://sorathe-sky-default-rtdb.asia-southeast1.firebasedatabase.app
FIREBASE_PROJECT_ID=sorathe-sky
FIREBASE_STORAGE_BUCKET=sorathe-sky.firebasestorage.app
FIREBASE_MESSAGING_SENDER_ID=997495721473
FIREBASE_APP_ID=1:997495721473:web:700585dfbc6afd4c97ba00

# Production Settings
FLASK_ENV=production
FLASK_DEBUG=False
SECRET_KEY=GENERATE_RANDOM_32_CHAR_STRING_FOR_PRODUCTION
JWT_SECRET_KEY=GENERATE_RANDOM_JWT_SECRET_FOR_PRODUCTION

# Google Drive API
DRIVE_API_KEY=AIzaSyAd2xbht3di_oA1nBhcme0LxiKAtPYH-vs

# Server Configuration
HOST=0.0.0.0
PORT=5000

# Performance Settings (optimized for production)
FIREBASE_SYNC_INTERVAL=600
MEMORY_CLEANUP_INTERVAL=1800

# Security Settings
CORS_ORIGINS=*
MAX_CONTENT_LENGTH=104857600

# Redirect Settings (first play redirect)
REDIR=True
REDIR_LINK=https://www.profitableratecpm.com/bwbfztbys?key=c729b3abd419817cab9b64723e1b6696

# Platform-specific settings (uncomment as needed)
# For Render:
# PYTHON_VERSION=3.12.3
# For Vercel:
# VERCEL=1
# For Railway:
# RAILWAY_STATIC_URL=https://your-app.railway.app
