#!/usr/bin/env python3
"""
Firebase Realtime Database integration for CDN Video Player
Uses pyrebase for Firebase operations with local caching
"""

import pyrebase
import json
import time
import os
from typing import Dict, Any, Optional
import threading
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Firebase configuration from environment variables
FIREBASE_CONFIG = {
    "apiKey": os.getenv('FIREBASE_API_KEY', 'AIzaSyDPybiL00qV5gid1oYclwFW7apLrSSiTQA'),
    "authDomain": os.getenv('FIREBASE_AUTH_DOMAIN', 'sorathe-sky.firebaseapp.com'),
    "databaseURL": os.getenv('FIREBASE_DATABASE_URL', 'https://sorathe-sky-default-rtdb.asia-southeast1.firebasedatabase.app'),
    "projectId": os.getenv('FIREBASE_PROJECT_ID', 'sorathe-sky'),
    "storageBucket": os.getenv('FIREBASE_STORAGE_BUCKET', 'sorathe-sky.firebasestorage.app'),
    "messagingSenderId": os.getenv('FIREBASE_MESSAGING_SENDER_ID', '997495721473'),
    "appId": os.getenv('FIREBASE_APP_ID', '1:997495721473:web:700585dfbc6afd4c97ba00'),
    "measurementId": os.getenv('FIREBASE_MEASUREMENT_ID', 'G-XTNMV1XLWN')
}

# Firebase-only configuration (no local cache)
SYNC_INTERVAL = int(os.getenv('FIREBASE_SYNC_INTERVAL', 600))  # Background sync interval

class PlayerDatabase:
    def __init__(self):
        """Initialize Firebase connection - Pure Firebase mode (no local cache)"""
        try:
            # Initialize Firebase
            firebase = pyrebase.initialize_app(FIREBASE_CONFIG)
            self.db = firebase.database()
            self.connected = True
            print("✅ Firebase connected successfully - Pure Firebase mode")
        except Exception as e:
            print(f"❌ Firebase connection failed: {e}")
            self.db = None
            self.connected = False

        self.lock = threading.Lock()

        if not self.connected:
            print("⚠️ Warning: Firebase not connected - application may not work properly")



    def get_all_videos(self) -> Dict[str, Any]:
        """Get all videos directly from Firebase"""
        if not self.connected:
            print("❌ Firebase not connected - cannot get videos")
            return {}

        try:
            firebase_data = self.db.child("player").child("videos").get()
            if firebase_data.val():
                videos = firebase_data.val()
                print(f"📥 Retrieved {len(videos)} videos from Firebase")
                return videos
            else:
                print("📭 No videos found in Firebase")
                return {}
        except Exception as e:
            print(f"❌ Firebase fetch error: {e}")
            return {}

    def get_video(self, video_id: str) -> Optional[Dict[str, Any]]:
        """Get specific video by ID directly from Firebase"""
        if not self.connected:
            print(f"❌ Firebase not connected - cannot get video {video_id}")
            return None

        try:
            firebase_data = self.db.child("player").child("videos").child(video_id).get()
            if firebase_data.val():
                print(f"📥 Retrieved video {video_id} from Firebase")
                return firebase_data.val()
            else:
                print(f"📭 Video {video_id} not found in Firebase")
                return None
        except Exception as e:
            print(f"❌ Firebase fetch error for video {video_id}: {e}")
            return None

    def save_video(self, video_id: str, video_data: Dict[str, Any]) -> bool:
        """Save video data directly to Firebase immediately"""
        if not self.connected:
            print(f"❌ Firebase not connected - cannot save video {video_id}")
            return False

        try:
            with self.lock:
                # Save directly to Firebase
                self.db.child("player").child("videos").child(video_id).set(video_data)
                print(f"💾 Video {video_id} saved to Firebase immediately")

                # Update stats in Firebase
                self._update_stats_firebase()
                return True

        except Exception as e:
            print(f"❌ Firebase save error for video {video_id}: {e}")
            return False

    def delete_video(self, video_id: str) -> bool:
        """Delete video directly from Firebase"""
        if not self.connected:
            print(f"❌ Firebase not connected - cannot delete video {video_id}")
            return False

        try:
            with self.lock:
                # Remove from Firebase
                self.db.child("player").child("videos").child(video_id).remove()
                print(f"🗑️ Video {video_id} deleted from Firebase")

                # Update stats in Firebase
                self._update_stats_firebase()
                return True

        except Exception as e:
            print(f"❌ Firebase delete error for video {video_id}: {e}")
            return False

    def update_video_views(self, video_id: str) -> bool:
        """Increment video view count directly in Firebase"""
        if not self.connected:
            print(f"❌ Firebase not connected - cannot update views for {video_id}")
            return False

        try:
            # Get current video data
            video_data = self.get_video(video_id)
            if video_data:
                video_data["views"] = video_data.get("views", 0) + 1
                video_data["last_viewed"] = time.time()

                # Save directly to Firebase
                self.db.child("player").child("videos").child(video_id).set(video_data)
                print(f"📊 Updated views for video {video_id} in Firebase")
                return True
        except Exception as e:
            print(f"❌ Firebase view update error for video {video_id}: {e}")
        return False

    def update_video_views_per_ip(self, video_id: str, ip_address: str) -> bool:
        """Increment video view count only once per IP address"""
        video_data = self.get_video(video_id)
        if video_data:
            # Initialize IP tracking if not exists
            if "viewed_ips" not in video_data:
                video_data["viewed_ips"] = []

            # Only count view if IP hasn't viewed before
            if ip_address not in video_data["viewed_ips"]:
                video_data["viewed_ips"].append(ip_address)
                video_data["views"] = video_data.get("views", 0) + 1
                video_data["last_viewed"] = time.time()
                print(f"🔥 New unique view for {video_id} from IP: {ip_address} (Total: {video_data['views']})")
                return self.save_video(video_id, video_data)
            else:
                print(f"♻️ Repeat view from IP: {ip_address} for {video_id} (not counted)")
                return True
        return False

    def update_video_views_per_session(self, video_id: str, session_id: str) -> bool:
        """Increment video view count only once per session (1 hour expiry)"""
        if not self.connected:
            print(f"❌ Firebase not connected - cannot update views for {video_id}")
            return False

        try:
            video_data = self.get_video(video_id)
            if video_data:
                current_time = time.time()
                session_duration = 3600  # 1 hour in seconds

                # Sanitize session ID for Firebase (remove invalid characters)
                import hashlib
                import re
                # Remove invalid Firebase characters and hash for consistency
                clean_session_id = re.sub(r'[.$#\[\]/]', '_', session_id)
                sanitized_session_id = hashlib.md5(clean_session_id.encode()).hexdigest()

                # Initialize session tracking if not exists
                if "viewed_sessions" not in video_data:
                    video_data["viewed_sessions"] = {}

                # Clean expired sessions
                expired_sessions = []
                for sid, timestamp in video_data["viewed_sessions"].items():
                    if current_time - timestamp > session_duration:
                        expired_sessions.append(sid)

                for sid in expired_sessions:
                    del video_data["viewed_sessions"][sid]

                # Only count view if session hasn't viewed before or session expired
                if sanitized_session_id not in video_data["viewed_sessions"]:
                    video_data["viewed_sessions"][sanitized_session_id] = current_time
                    video_data["views"] = video_data.get("views", 0) + 1
                    video_data["last_viewed"] = current_time

                    # Save directly to Firebase
                    self.db.child("player").child("videos").child(video_id).set(video_data)
                    print(f"🔥 New session view for {video_id} from session: {sanitized_session_id[:12]}... (Total: {video_data['views']})")
                    return True
                else:
                    print(f"♻️ Repeat view from session: {sanitized_session_id[:12]}... for {video_id} (not counted)")
                    return True
        except Exception as e:
            print(f"❌ Firebase session view update error for video {video_id}: {e}")
        return False

    def _update_stats_firebase(self):
        """Update global statistics directly in Firebase"""
        if not self.connected:
            return

        try:
            videos = self.get_all_videos()
            total_videos = len(videos)
            total_views = sum(video.get("views", 0) for video in videos.values())

            stats = {
                "total_videos": total_videos,
                "total_views": total_views,
                "last_updated": time.time()
            }

            self.db.child("player").child("stats").set(stats)
            print(f"📊 Updated stats in Firebase: {total_videos} videos, {total_views} views")
        except Exception as e:
            print(f"❌ Firebase stats update error: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """Get database statistics directly from Firebase"""
        if not self.connected:
            return {"total_videos": 0, "total_views": 0}

        try:
            firebase_data = self.db.child("player").child("stats").get()
            if firebase_data.val():
                return firebase_data.val()
            else:
                # Calculate stats if not exists
                videos = self.get_all_videos()
                stats = {
                    "total_videos": len(videos),
                    "total_views": sum(video.get("views", 0) for video in videos.values()),
                    "last_updated": time.time()
                }
                self.db.child("player").child("stats").set(stats)
                return stats
        except Exception as e:
            print(f"❌ Firebase stats fetch error: {e}")
            return {"total_videos": 0, "total_views": 0}

    def cleanup_expired_videos(self) -> int:
        """Remove expired videos from database"""
        current_time = time.time()
        expired_count = 0

        videos = self.get_all_videos().copy()
        for video_id, video_data in videos.items():
            if current_time > video_data.get("expires_at", 0):
                if self.delete_video(video_id):
                    expired_count += 1

        if expired_count > 0:
            print(f"🧹 Cleaned up {expired_count} expired videos")

        return expired_count

    def force_refresh_from_firebase(self):
        """Force refresh stats from Firebase (no cache to refresh)"""
        if not self.connected:
            print("❌ Firebase not connected - cannot refresh")
            return False

        try:
            # Just update stats since we're working directly with Firebase
            self._update_stats_firebase()
            videos = self.get_all_videos()
            video_count = len(videos)
            print(f"🔄 Verified {video_count} videos in Firebase")
            return True
        except Exception as e:
            print(f"❌ Force refresh error: {e}")
            return False

    def get_connection_status(self):
        """Get detailed connection and data status"""
        video_count = 0
        if self.connected:
            try:
                videos = self.get_all_videos()
                video_count = len(videos)
            except:
                pass

        return {
            "firebase_connected": self.connected,
            "firebase_videos": video_count,
            "mode": "pure_firebase",
            "cache_disabled": True,
            "stats": self.get_stats()
        }

# Global database instance
player_db = PlayerDatabase()