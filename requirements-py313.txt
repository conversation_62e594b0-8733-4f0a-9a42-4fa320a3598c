# CDN Video Player - Python 3.13+ Compatible Requirements
# Use this for Python 3.13+ deployments to avoid gevent compilation issues

# Core Flask dependencies
Flask>=2.0.0,<4.0.0
Flask-CORS>=3.0.0,<5.0.0
Werkzeug>=2.0.0,<4.0.0

# Security & JWT for URL masking
PyJWT>=2.4.0,<3.0.0

# HTTP and networking (compatible with pyrebase4)
requests>=2.25.0,<2.32.0
urllib3>=1.26.0,<2.0.0
certifi>=2021.10.8
charset-normalizer>=2.0.0,<4.0.0
idna>=2.10,<4.0.0

# Firebase integration for video storage
pyrebase4>=4.5.0,<5.0.0

# Environment and system utilities
python-dotenv>=0.19.0,<2.0.0
psutil>=5.8.0,<7.0.0

# Production server (Python 3.13 compatible - no gevent)
gunicorn>=20.1.0,<24.0.0
# Note: eventlet and gevent removed due to Python 3.13 compilation issues
# Alternative ASGI server option:
# uvicorn[standard]>=0.20.0,<1.0.0

# Build tools
setuptools>=68.0.0,<75.0.0
wheel>=0.37.0,<1.0.0

# Flask core dependencies
Jinja2>=3.0.0,<4.0.0
MarkupSafe>=2.0.0,<3.0.0
itsdangerous>=2.0.0,<3.0.0
click>=8.0.0,<9.0.0
blinker>=1.4.0,<2.0.0

# Python compatibility
six>=1.16.0,<2.0.0
typing-extensions>=4.0.0,<5.0.0

# Optional: Enhanced video processing (uncomment if needed)
# opencv-python>=4.5.0,<5.0.0
# Pillow>=8.0.0,<11.0.0

# Development dependencies (optional)
# pytest>=6.0.0,<8.0.0
# black>=21.0.0,<24.0.0
# flake8>=3.9.0,<7.0.0
