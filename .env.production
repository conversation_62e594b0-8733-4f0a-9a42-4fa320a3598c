# Production Environment Configuration for CDN Video Player
# Copy this to .env and fill in your actual values

# Flask Configuration
FLASK_ENV=production
SECRET_KEY=your_secret_key_here_generate_with_openssl_rand_hex_32

# Firebase Configuration - sorathe-sky project
FIREBASE_DATABASE_URL=https://sorathe-sky-default-rtdb.asia-southeast1.firebasedatabase.app
FIREBASE_API_KEY=AIzaSyDPybiL00qV5gid1oYclwFW7apLrSSiTQA
FIREBASE_AUTH_DOMAIN=sorathe-sky.firebaseapp.com
FIREBASE_PROJECT_ID=sorathe-sky
FIREBASE_STORAGE_BUCKET=sorathe-sky.firebasestorage.app
FIREBASE_MESSAGING_SENDER_ID=997495721473
FIREBASE_APP_ID=1:997495721473:web:700585dfbc6afd4c97ba00

# Google Drive API Configuration
DRIVE_API=AIzaSyAd2xbht3di_oA1nBhcme0LxiKAtPYH-vs
DRIVE_STREAM_URL=https://www.googleapis.com/drive/v3/files/{fileid}?alt=media&key={APIKey}

# Redirect Settings (first play redirect)
REDIR=True
REDIR_LINK=https://www.profitableratecpm.com/bwbfztbys?key=c729b3abd419817cab9b64723e1b6696

# Performance Settings
MAX_CONCURRENT_USERS=100
SESSION_TIMEOUT=1800
MEMORY_CLEANUP_INTERVAL=300
MAX_CACHE_SIZE=50

# Security Settings
RATE_LIMIT_PER_MINUTE=60
ENABLE_BOT_PROTECTION=true
REQUIRE_REFERER_FOR_STREAMING=true

# Logging
LOG_LEVEL=INFO
ACCESS_LOG_FORMAT=combined

# Docker specific
PYTHONOPTIMIZE=1
PYTHONDONTWRITEBYTECODE=1
PYTHONUNBUFFERED=1

# Memory optimization
MALLOC_ARENA_MAX=2
MALLOC_MMAP_THRESHOLD_=131072
MALLOC_TRIM_THRESHOLD_=131072
MALLOC_TOP_PAD_=131072
MALLOC_MMAP_MAX_=65536
